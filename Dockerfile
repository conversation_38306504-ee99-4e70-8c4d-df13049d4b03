# Use the official Python 3.8 image
FROM python:3.8-slim

<PERSON>NV http_proxy="http://**********:8088"
ENV https_proxy="http://**********:8088"

RUN apt-get update
RUN apt-get upgrade -y
RUN apt-get install -y ca-certificates telnet ldap-utils vim iputils-ping procps

RUN apt-get update && apt-get install -y \
    gcc \
    build-essential \
    libssl-dev \
    libffi-dev \
    && rm -rf /var/lib/apt/lists/*

# Set the working directory in the container
WORKDIR /app

# Copy the requirements.txt file into the container
COPY requirements.txt .

# Install the dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy the rest of the application code into the container
# COPY . .

# Ensure /app is owned by the host user
# RUN chown -R ${UID}:${GID} /app

# Expose the port on which the app will run
# EXPOSE 3009

# Command to run the FastAPI app using uvicorn
# CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "3009"]
# CMD ["hypercorn", "main:app", "--bind", "0.0.0.0:3009", "--log-level", "DEBUG", "--workers", "3", "--reload"]
