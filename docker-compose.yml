version: '3.8'

services:

  # Redis (optional, for caching)
  redis:
    image: redis:7-alpine
    container_name: zds_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - zds_network
    restart: unless-stopped
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # FastAPI Application
  app:
    build: .
    container_name: zds_app
    environment:
      # Database configuration (from .env file)
      PG_HOST: ${PG_HOST}
      PG_PORT: ${PG_PORT}
      PG_USER: ${PG_USER}
      PG_PASSWORD: ${PG_PASSWORD}
      PG_DB: ${PG_DB}
      PG_SCHEMA: ${PG_SCHEMA}
      PG_DBTYPE: ${PG_DBTYPE}

      # Application configuration
      BASE_URL: ${BASE_URL:-/api/v1}
      FE_URL: ${FE_URL:-http://localhost:3000}
      BE_URL: ${BE_URL:-http://localhost:3014}
      EXCLUDE_PATH: ${EXCLUDE_PATH:-}

      # Security
      SECRET_KEY: ${SECRET_KEY:-your-secret-key-here}

      # Redis (optional)
      REDIS_URL: redis://redis:6379/0
    volumes:
      - ./app:/app/app
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    ports:
      - "3014:3014"
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - zds_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3014/docs"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  redis_data:
    driver: local

networks:
  zds_network:
    driver: bridge
