{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Test LDAP Authentication\n", "Test các phư<PERSON><PERSON> thức authentication khác nhau với LDAP server"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Import c<PERSON><PERSON> thư viện c<PERSON>n thiết\n", "import ldap\n", "import ldap3\n", "import json\n", "from datetime import datetime"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Test với python-ldap"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <PERSON><PERSON><PERSON> hình LDAP\n", "LDAP_SERVER = \"ldaps://ldap.vng.com.vn:636\"\n", "BASE_DN = \"ou=People,dc=vng,dc=com,dc=vn\"\n", "SERVICE_DN = \"cn=zds.wl\"\n", "SERVICE_PASSWORD = \"2c018d791f5c72a2e7de07113937a881bda54689\"\n", "\n", "def test_python_ldap(username, password):\n", "    try:\n", "        # Khởi tạo connection\n", "        ldap_client = ldap.initialize(LDAP_SERVER)\n", "        ldap_client.protocol_version = ldap.VERSION3\n", "        ldap_client.set_option(ldap.OPT_REFERRALS, 0)\n", "        \n", "        # Enable TLS\n", "        # ldap_client.set_option(ldap.OPT_X_TLS_REQUIRE_CERT, ldap.OPT_X_TLS_NEVER)\n", "        # ldap_client.start_tls_s()\n", "\n", "        # Bind với service account\n", "        ldap_client.simple_bind_s(SERVICE_DN, SERVICE_PASSWORD)\n", "\n", "        # Search user\n", "        search_filter = f\"(uid={username})\"\n", "        attributes = ['cn', 'uid', 'displayName']\n", "        \n", "        result = ldap_client.search_s(\n", "            BASE_DN,\n", "            ldap.SCOPE_SUBTREE,\n", "            search_filter,\n", "            attributes\n", "        )\n", "\n", "        if not result:\n", "            return {\"status\": \"error\", \"message\": \"User not found\"}\n", "\n", "        # Get user DN và thử bind\n", "        user_dn = result[0][0]\n", "        try:\n", "            ldap_client.simple_bind_s(user_dn, password)\n", "            user_attrs = result[0][1]\n", "            return {\n", "                \"status\": \"success\",\n", "                \"user\": {\n", "                    \"username\": user_attrs['uid'][0].decode('utf-8'),\n", "                    \"display_name\": user_attrs['displayName'][0].decode('utf-8') if 'displayName' in user_attrs else \"\"\n", "                }\n", "            }\n", "        except ldap.INVALID_CREDENTIALS:\n", "            return {\"status\": \"error\", \"message\": \"Invalid password\"}\n", "\n", "    except ldap.LDAPError as e:\n", "        return {\"status\": \"error\", \"message\": str(e)}\n", "    finally:\n", "        ldap_client.unbind_s()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. <PERSON> với ldap3"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def test_ldap3(username, password):\n", "    # Khởi tạo server\n", "    server = ldap3.Server(\n", "        LDAP_SERVER,\n", "        use_ssl=True,\n", "        get_info=ldap3.ALL\n", "    )\n", "\n", "    try:\n", "        # Bind với service account\n", "        service_conn = ldap3.Connection(\n", "            server,\n", "            user=SERVICE_DN,\n", "            password=SERVICE_PASSWORD,\n", "            authentication=ldap3.SIMPLE\n", "        )\n", "\n", "        if not service_conn.bind():\n", "            return {\"status\": \"error\", \"message\": \"Service account bind failed\"}\n", "\n", "        # Search user\n", "        search_success = service_conn.search(\n", "            BASE_DN,\n", "            f\"(uid={username})\",\n", "            attributes=['*']  # <PERSON><PERSON><PERSON> tất cả attributes\n", "        )\n", "\n", "        if not search_success or not service_conn.entries:\n", "            return {\"status\": \"error\", \"message\": \"User not found\"}\n", "\n", "        user_entry = service_conn.entries[0]\n", "        user_dn = user_entry.entry_dn\n", "        \n", "        # Test user authentication tr<PERSON><PERSON><PERSON> khi lấy thông tin\n", "        user_conn = ldap3.Connection(\n", "            server,\n", "            user=user_dn,\n", "            password=password,\n", "            authentication=ldap3.SIMPLE\n", "        )\n", "\n", "        if not user_conn.bind():\n", "            return {\"status\": \"error\", \"message\": \"Invalid password\"}\n", "\n", "        # Lấy manager <PERSON><PERSON> attribute manager\n", "        manager_dn = user_entry.manager.value if hasattr(user_entry, 'manager') else None\n", "        manager_username = None\n", "        \n", "        # <PERSON><PERSON><PERSON> c<PERSON> manager <PERSON><PERSON>, search để lấy username của manager\n", "        if manager_dn:\n", "            manager_cn = manager_dn.split(',')[0].replace('cn=', '')\n", "            print(f\"Manager CN: {manager_cn}\")  # Debug print\n", "            manager_search = service_conn.search(\n", "                BASE_DN,\n", "                f\"(cn={manager_cn})\",  # Search bằng CN\n", "                attributes=['uid']\n", "            )\n", "            if manager_search and service_conn.entries:\n", "                manager_username = service_conn.entries[0].uid.value\n", "\n", "        # Tạo user profile v<PERSON><PERSON> kiểm tra null safety\n", "        print(user_entry)\n", "        user_profile = {\n", "            \"username\": user_entry.uid.value if hasattr(user_entry, 'uid') else None,\n", "            \"display_name\": user_entry.displayName.value if hasattr(user_entry, 'displayName') else None,\n", "            \"given_name\": user_entry.givenName.value if hasattr(user_entry, 'givenName') else None,\n", "            \"sn\": user_entry.sn.value if hasattr(user_entry, 'sn') else None,\n", "            \"department_code\": user_entry.departmentcode.value if hasattr(user_entry, 'departmentcode') else None,\n", "            \"department\": user_entry.department.value if hasattr(user_entry, 'department') else None,\n", "            \"job_title\": user_entry.jobtitle.value if hasattr(user_entry, 'jobtitle') else None,\n", "            \"manager\": manager_username\n", "        }\n", "\n", "        return {\n", "            \"status\": \"success\",\n", "            \"user\": user_profile\n", "        }\n", "\n", "    except ldap3.core.exceptions.LDAPException as e:\n", "        return {\"status\": \"error\", \"message\": str(e)}\n", "    finally:\n", "        if 'service_conn' in locals():\n", "            service_conn.unbind()\n", "        if 'user_conn' in locals():\n", "            user_conn.unbind()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Run Tests"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Testing with ldap3:\n", "Manager CN: 1c5821b4-8abf-42a4-822e-d2470d8fa330\n", "DN: cn=8e112a49-78aa-4d85-bc56-2880b4b74ef6,ou=People,dc=vng,dc=com,dc=vn - STATUS: Read - READ TIME: 2025-04-28T15:18:40.920300\n", "    businessphones: 0962021854\n", "    cn: 8e112a49-78aa-4d85-bc56-2880b4b74ef6\n", "    companyname: Zion\n", "    country: VN\n", "    department: Zalopay Data Services\n", "    departmentcode: ZDS\n", "    departmentid: 17040\n", "    displayName: <PERSON><PERSON><PERSON> <PERSON><PERSON> (2)\n", "    division: Payment\n", "    divisioncode: PY\n", "    divisionid: 16231\n", "    employeeid: ZN-00702\n", "    givenName: <PERSON><PERSON>\n", "    jobtitle: Staff Data Engineer\n", "    mail: <EMAIL>\n", "    mailnickname: huyhh2\n", "    manager: cn=1c5821b4-8abf-42a4-822e-d2470d8fa330,ou=People,dc=vng,dc=com,dc=vn\n", "    memberOf: cn=158cde2b-9498-4eb3-a745-542d4441553c,ou=Groups,dc=vng,dc=com,dc=vn\n", "              cn=1df6f3fc-52e8-4c97-b230-643dd72387a2,ou=Groups,dc=vng,dc=com,dc=vn\n", "              cn=ee75827a-47a5-470b-9970-13747088aa8f,ou=Groups,dc=vng,dc=com,dc=vn\n", "              cn=8cc65bad-f177-4baf-8e7e-55d59c4c93a7,ou=Groups,dc=vng,dc=com,dc=vn\n", "              cn=04f3a821-e5ad-44f6-85df-38be8ee69977,ou=Groups,dc=vng,dc=com,dc=vn\n", "              cn=121f8d99-5d46-482f-b69d-b8d622255a0c,ou=Groups,dc=vng,dc=com,dc=vn\n", "    mobilephone: (+84) 0912260392\n", "    objectClass: top\n", "                 person\n", "                 organizational<PERSON>erson\n", "                 <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "                 extensibleObject\n", "    officelocation: VNG Campus\n", "    sn: <PERSON><PERSON><PERSON>\n", "    uid: huyhh2\n", "    usagelocation: VN\n", "    usertype: Member\n", "\n", "{\n", "  \"status\": \"success\",\n", "  \"user\": {\n", "    \"username\": \"huyhh2\",\n", "    \"display_name\": \"<PERSON><PERSON><PERSON>\\u00e0ng H\\u1ea3i (2)\",\n", "    \"given_name\": \"<PERSON><PERSON>\",\n", "    \"sn\": \"Ho\\u00e0ng H\\u1ea3i\",\n", "    \"department_code\": \"ZDS\",\n", "    \"department\": \"Zalopay Data Services\",\n", "    \"job_title\": \"Staff Data Engineer\",\n", "    \"manager_username\": \"vinhtx2\"\n", "  }\n", "}\n"]}], "source": ["# Test credentials\n", "test_username = \"huyhh2\"  # Thay bằng username thật\n", "test_password = \"121122\" + \"320866\"  # Thay bằng password thật\n", "\n", "# # Test với python-ldap\n", "# print(\"Testing with python-ldap:\")\n", "# result1 = test_python_ldap(test_username, test_password)\n", "# print(json.dumps(result1, indent=2))\n", "\n", "print(\"\\nTesting with ldap3:\")\n", "result2 = test_ldap3(test_username, test_password)\n", "print(json.dumps(result2, indent=2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Test Performance"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Performance test with python-ldap:\n", "{\n", "  \"average_time\": 0.03505916595458984,\n", "  \"min_time\": 0.03170609474182129,\n", "  \"max_time\": 0.04249882698059082\n", "}\n", "\n", "Performance test with ldap3:\n", "{\n", "  \"average_time\": 0.35245118141174314,\n", "  \"min_time\": 0.3175640106201172,\n", "  \"max_time\": 0.42739391326904297\n", "}\n"]}], "source": ["import time\n", "\n", "def measure_performance(func, username, password, iterations=5):\n", "    times = []\n", "    for _ in range(iterations):\n", "        start = time.time()\n", "        result = func(username, password)\n", "        end = time.time()\n", "        times.append(end - start)\n", "    \n", "    avg_time = sum(times) / len(times)\n", "    return {\n", "        \"average_time\": avg_time,\n", "        \"min_time\": min(times),\n", "        \"max_time\": max(times)\n", "    }\n", "\n", "print(\"Performance test with python-ldap:\")\n", "perf1 = measure_performance(test_python_ldap, test_username, test_password)\n", "print(json.dumps(perf1, indent=2))\n", "\n", "print(\"\\nPerformance test with ldap3:\")\n", "perf2 = measure_performance(test_ldap3, test_username, test_password)\n", "print(json.dumps(perf2, indent=2))"]}], "metadata": {"kernelspec": {"display_name": "env217", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}