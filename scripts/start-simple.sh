#!/bin/bash

# Simple startup script (app only, using existing database)
echo "🚀 Starting ZDS Platform (App Only)..."

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please update .env file with your database configuration"
    exit 1
fi

# Create necessary directories
mkdir -p uploads/user_files
mkdir -p logs

# Start app only
echo "🐳 Starting Docker container (app only)..."
docker-compose -f docker-compose.simple.yml up --build

echo "✅ Application started!"
echo "🌐 API Documentation: http://localhost:3014/docs"
echo "📊 Database: Using your existing PostgreSQL connection"
