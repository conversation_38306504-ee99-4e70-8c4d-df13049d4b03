#!/bin/bash

# Production startup script
echo "🚀 Starting ZDS Platform in Production Mode..."

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ .env file not found! Please create it from .env.example"
    exit 1
fi

# Create necessary directories
mkdir -p uploads/user_files
mkdir -p logs

# Start services
echo "🐳 Starting Docker containers in production mode..."
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d --build

echo "✅ Production environment started!"
echo "🌐 API: http://localhost:3014"
echo "📊 Check logs: docker-compose logs -f app"
