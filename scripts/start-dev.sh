#!/bin/bash

# Development startup script
echo "🚀 Starting ZDS Platform in Development Mode..."

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please update .env file with your configuration"
fi

# Create necessary directories
mkdir -p uploads/user_files
mkdir -p logs

# Start services
echo "🐳 Starting Docker containers..."
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --build

echo "✅ Development environment started!"
echo "🌐 API Documentation: http://localhost:3014/docs"
echo "📊 Database: Using your existing PostgreSQL connection"
echo "⚠️  Make sure your database is running and accessible"
