version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      # Production settings
      DEBUG: "false"
      RELOAD: "false"
    command: ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "3014", "--workers", "4"]
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    restart: always

  redis:
    deploy:
      resources:
        limits:
          cpus: '0.25'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 128M
    restart: always
