import os
from fastapi import APIRouter, Depends, HTTPException, Body
from sqlalchemy.ext.asyncio import AsyncSession
import json

from services import service_session, service_user, crud_release
from schemas import schemas_release
from core.errors import AuthorizationError, ValidationError, DatabaseError, DuplicateError, NotFoundError
from core.logger import log_info, log_error, log_warning

BASE_URL = os.environ.get("BASE_URL")

itemrouter = APIRouter()


@itemrouter.get(f"{BASE_URL}/release/notes", tags=["release"])
async def get_all_release_notes(
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user)
):
    """Get all release notes"""
    try:
        return await crud_release.get_all_release_notes(session)
    except DatabaseError as e:
        log_error(
            e,
            context="get_all_release_notes",
            user=current_user.get('username')
        )
        raise HTTPException(status_code=500, detail=str(e))


@itemrouter.get(f"{BASE_URL}/release/note", tags=["release"])
async def get_release_note(
    note_id: int,
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user)
):
    """Get a release note by ID"""
    try:
        return await crud_release.get_release_note_by_id(session, note_id)
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except DatabaseError as e:
        log_error(
            e,
            context="get_release_note",
            user=current_user.get('username'),
            additional_info={"note_id": note_id}
        )
        raise HTTPException(status_code=500, detail=str(e))


@itemrouter.post(f"{BASE_URL}/release/note/create", tags=["release"])
async def create_release_note(
    body: schemas_release.ReleaseNoteCreate = Body(...),
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user)
):
    """Create a new release note"""
    # Check authorization
    if "admin" not in current_user.get("role", []):
        log_warning(
            f"Unauthorized attempt to create release note by {current_user.get('username')}",
            context="create_release_note"
        )
        raise AuthorizationError("Only admin users can create release notes")

    try:
        release_note = await crud_release.create_release_note(session, body.dict())
        return release_note
    except DuplicateError as e:
        raise HTTPException(status_code=409, detail=str(e))
    except DatabaseError as e:
        log_error(
            e,
            context="create_release_note",
            user=current_user.get('username'),
            additional_info={"release_note": body.dict()}
        )
        raise HTTPException(status_code=500, detail=str(e))


@itemrouter.post(f"{BASE_URL}/release/note/update", tags=["release"])
async def update_release_note(
    note_id: int,
    body: schemas_release.ReleaseNoteUpdate = Body(...),
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user)
):
    """Update an existing release note"""
    # Check authorization
    if "admin" not in current_user.get("role", []):
        log_warning(
            f"Unauthorized attempt to update release note by {current_user.get('username')}",
            context="update_release_note"
        )
        raise AuthorizationError("Only admin users can update release notes")

    try:
        # Filter out None values
        update_data = {k: v for k, v in body.dict().items() if v is not None}
        
        if not update_data:
            raise ValidationError("No fields to update")
            
        release_note = await crud_release.update_release_note(session, note_id, update_data)
        return release_note
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except DatabaseError as e:
        log_error(
            e,
            context="update_release_note",
            user=current_user.get('username'),
            additional_info={"note_id": note_id, "update_data": body.dict()}
        )
        raise HTTPException(status_code=500, detail=str(e))


@itemrouter.post(f"{BASE_URL}/release/note/delete", tags=["release"])
async def delete_release_note(
    note_id: int,
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user)
):
    """Delete a release note"""
    # Check authorization
    if "admin" not in current_user.get("role", []):
        log_warning(
            f"Unauthorized attempt to delete release note by {current_user.get('username')}",
            context="delete_release_note"
        )
        raise AuthorizationError("Only admin users can delete release notes")

    try:
        release_note = await crud_release.delete_release_note(session, note_id)
        return {"message": "Release note deleted successfully", "release_note": release_note}
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except DatabaseError as e:
        log_error(
            e,
            context="delete_release_note",
            user=current_user.get('username'),
            additional_info={"note_id": note_id}
        )
        raise HTTPException(status_code=500, detail=str(e))
