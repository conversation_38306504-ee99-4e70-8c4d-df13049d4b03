import os

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Query
from fastapi.responses import FileResponse
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional

from schemas import schemas_files
from services import service_user, service_session, crud_file as crud
from services.file_service import file_service
from core.errors import DatabaseError, ValidationError, NotFoundError
from core.logger import log_info, log_error, log_warning

import core.config as config
config.get_config()

BASE_URL = os.environ.get("BASE_URL")

itemrouter = APIRouter()


@itemrouter.post(f"{BASE_URL}/files/upload", response_model=schemas_files.FileUploadResponse, tags=["files"])
async def upload_file(
    file: UploadFile = File(...),
    description: Optional[str] = Form(None),
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user)
):
    """
    Upload a file

    Form data should contain:
    - file: Binary file data (required)
    - description: File description (optional)
    """
    try:
        # Save file to disk
        file_data = await file_service.save_file(file, current_user.get("id"), description)

        # Create database record
        file_record = await crud.create_file_record(session, file_data)

        log_info(f"File uploaded successfully by user {current_user.get('username')}: {file.filename}")

        return schemas_files.FileUploadResponse(
            message="File uploaded successfully",
            file_id=file_record.get("id"),
            filename=file_record.get("filename"),
            original_filename=file_record.get("original_filename"),
            file_size=file_record.get("file_size"),
            file_size_mb=file_service.get_file_size_mb(file_record.get("file_size")),
            status="success"
        )
        # raise HTTPException(status_code=400, detail=str(e))

    except ValidationError as e:
        log_warning(f"Validation error uploading file: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except DatabaseError as e:
        log_error(f"Database error uploading file: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        log_error(f"Unexpected error uploading file: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@itemrouter.get(f"{BASE_URL}/files/download", tags=["files"])
async def download_file(
    file_id: int = Query(..., description="File ID to download"),
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user)
):
    """
    Download a file by ID
    """
    try:
        # Get file record
        file_record = await crud.get_file_by_id(session, file_id)

        if not file_record:
            raise HTTPException(status_code=404, detail="File not found")

        # Check if user can access this file (owner or admin)
        if file_record.get("user_id") != current_user.get("id") and current_user.get("role") != "admin":
            raise HTTPException(status_code=403, detail="Access denied")

        # Check if file exists on disk
        if not file_service.file_exists(file_record.get("filename"), file_record.get("user_id")):
            log_error(f"File not found on disk: {file_record.get('filename')}")
            raise HTTPException(status_code=404, detail="File not found on server")

        # Get file path
        file_path = file_service.get_file_path(file_record.get("filename"), file_record.get("user_id"))

        log_info(f"File downloaded by user {current_user.get('username')}: {file_record.get('original_filename')}")

        return FileResponse(
            path=str(file_path),
            filename=file_record.get("original_filename"),
            media_type=file_record.get("mime_type") or "application/octet-stream"
        )

    except HTTPException:
        raise
    except DatabaseError as e:
        log_error(f"Database error downloading file: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        log_error(f"Unexpected error downloading file: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@itemrouter.delete(f"{BASE_URL}/files/delete", response_model=schemas_files.FileDeleteResponse, tags=["files"])
async def delete_file(
    file_id: int = Query(..., description="File ID to delete"),
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user)
):
    """
    Delete a file by ID
    """
    try:
        # Get file record first
        file_record = await crud.get_file_by_id(session, file_id)

        if not file_record:
            raise HTTPException(status_code=404, detail="File not found")

        # Delete from database (soft delete)
        await crud.delete_file_record(session, file_id, current_user.get("id"))

        # Delete from disk
        file_service.delete_file(file_record.get("filename"), file_record.get("user_id"))

        log_info(f"File deleted by user {current_user.get('username')}: {file_record.get('original_filename')}")

        return schemas_files.FileDeleteResponse(
            message="File deleted successfully",
            file_id=file_id,
            status="success"
        )

    except ValidationError as e:
        log_warning(f"Validation error deleting file: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except DatabaseError as e:
        log_error(f"Database error deleting file: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        log_error(f"Unexpected error deleting file: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@itemrouter.get(f"{BASE_URL}/files/get_by_user", response_model=schemas_files.FileListResponse, tags=["files"])
async def get_my_files(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Items per page"),
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user)
):
    """
    Get current user's files with pagination
    """
    try:
        result = await crud.get_files_by_user_id(session, current_user.get("id"), page, limit)

        # Convert to response format
        files = []
        for file_record in result["files"]:
            file_info = schemas_files.FileInfo(
                id=file_record.get("id"),
                filename=file_record.get("filename"),
                original_filename=file_record.get("original_filename"),
                file_path=file_record.get("file_path"),
                file_size=file_record.get("file_size"),
                file_size_mb=file_service.get_file_size_mb(file_record.get("file_size")),
                mime_type=file_record.get("mime_type"),
                description=file_record.get("description"),
                user_id=file_record.get("user_id"),
                status=file_record.get("status"),
                created_at=file_record.get("created_at"),
                updated_at=file_record.get("updated_at")
            )
            files.append(file_info)

        return schemas_files.FileListResponse(
            files=files,
            total_count=result["total_count"],
            page=result["page"],
            limit=result["limit"]
        )

    except DatabaseError as e:
        log_error(f"Database error retrieving user files: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        log_error(f"Unexpected error retrieving user files: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@itemrouter.get(f"{BASE_URL}/files/get_all", response_model=schemas_files.FileListResponse, tags=["files"])
async def get_all_files(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Items per page"),
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user)
):
    """
    Get all files (admin only) with pagination
    """
    try:
        # Check admin permission
        if current_user.get("role") != "admin":
            raise HTTPException(status_code=403, detail="Admin access required")

        result = await crud.get_all_files(session, page, limit)

        # Convert to response format
        files = []
        for file_record in result["files"]:
            file_info = schemas_files.FileInfo(
                id=file_record.get("id"),
                filename=file_record.get("filename"),
                original_filename=file_record.get("original_filename"),
                file_path=file_record.get("file_path"),
                file_size=file_record.get("file_size"),
                file_size_mb=file_service.get_file_size_mb(file_record.get("file_size")),
                mime_type=file_record.get("mime_type"),
                description=file_record.get("description"),
                user_id=file_record.get("user_id"),
                status=file_record.get("status"),
                created_at=file_record.get("created_at"),
                updated_at=file_record.get("updated_at")
            )
            files.append(file_info)

        return schemas_files.FileListResponse(
            files=files,
            total_count=result["total_count"],
            page=result["page"],
            limit=result["limit"]
        )

    except HTTPException:
        raise
    except DatabaseError as e:
        log_error(f"Database error retrieving all files: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        log_error(f"Unexpected error retrieving all files: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
