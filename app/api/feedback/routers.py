import os
import json

from fastapi import APIRouter, Depends, HTTPException, Body
from sqlalchemy.ext.asyncio import AsyncSession

from schemas import schemas_feedback
from services import service_user, service_session, crud_feedback as crud
from core.errors import DatabaseError, ValidationError
from core.logger import log_info, log_error, log_warning

import core.config as config
config.get_config()

BASE_URL = os.environ.get("BASE_URL")

itemrouter = APIRouter()


@itemrouter.post(f"{BASE_URL}/feedback/create", response_model=schemas_feedback.FeedbackCreateResponse, tags=["feedback"])
async def create_feedback(
    body: schemas_feedback.FeedbackCreate = Body(...),
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user)
):
    """
    Create a new feedback entry
    
    Request body should contain:
    - subject: Feedback subject (required)
    - category: Feedback category (required) 
    - description: Detailed description (required)
    - priority: Priority level (required)
    - attachments: List of attachment info (optional)
    """
    try:
        # Prepare feedback data
        feedback_data = body.dict()
        feedback_data["user_id"] = current_user.get("id")
        
        # Convert attachments to JSON if present
        if feedback_data.get("attachments"):
            feedback_data["attachments"] = json.dumps([att.dict() for att in body.attachments])
        else:
            feedback_data["attachments"] = None
            
        # Validate priority values
        valid_priorities = ["low", "medium", "high", "urgent"]
        if feedback_data["priority"].lower() not in valid_priorities:
            raise ValidationError(f"Priority must be one of: {', '.join(valid_priorities)}")
        
        # Create feedback
        feedback = await crud.create_feedback(session, feedback_data)
        
        log_info(f"Feedback created successfully by user {current_user.get('username')}: {feedback.get('subject')}")
        
        return schemas_feedback.FeedbackCreateResponse(
            message="Feedback created successfully",
            feedback_id=feedback.get("id"),
            status="success"
        )
        
    except ValidationError as e:
        log_warning(f"Validation error creating feedback: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except DatabaseError as e:
        log_error(f"Database error creating feedback: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        log_error(f"Unexpected error creating feedback: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@itemrouter.get(f"{BASE_URL}/feedback/get", response_model=schemas_feedback.FeedbackResponse, tags=["feedback"])
async def get_feedback(
    feedback_id: int,
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user)
):
    """
    Get feedback by ID
    """
    try:
        feedback = await crud.get_feedback_by_id(session, feedback_id)
        
        if not feedback:
            raise HTTPException(status_code=404, detail="Feedback not found")
            
        # Check if user can access this feedback (owner or admin)
        if feedback.get("user_id") != current_user.get("id") and current_user.get("role") != "admin":
            raise HTTPException(status_code=403, detail="Access denied")
            
        # Parse attachments JSON if present
        attachments = None
        if feedback.get("attachments"):
            try:
                attachments = json.loads(feedback.get("attachments"))
            except json.JSONDecodeError:
                attachments = []
                
        return schemas_feedback.FeedbackResponse(
            id=feedback.get("id"),
            subject=feedback.get("subject"),
            category=feedback.get("category"),
            description=feedback.get("description"),
            priority=feedback.get("priority"),
            attachments=attachments,
            user_id=feedback.get("user_id"),
            status=feedback.get("status"),
            created_at=feedback.get("created_at"),
            updated_at=feedback.get("updated_at")
        )
        
    except HTTPException:
        raise
    except DatabaseError as e:
        log_error(f"Database error retrieving feedback: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        log_error(f"Unexpected error retrieving feedback: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@itemrouter.get(f"{BASE_URL}/feedback/get_by_user", tags=["feedback"])
async def get_my_feedback(
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user)
):
    """
    Get all feedback entries for the current user
    """
    try:
        feedback_list = await crud.get_feedback_by_user_id(session, current_user.get("id"))
        
        # Parse attachments for each feedback
        result = []
        for feedback in feedback_list:
            feedback_dict = dict(feedback)
            if feedback_dict.get("attachments"):
                try:
                    feedback_dict["attachments"] = json.loads(feedback_dict["attachments"])
                except json.JSONDecodeError:
                    feedback_dict["attachments"] = []
            result.append(feedback_dict)
            
        return result
        
    except DatabaseError as e:
        log_error(f"Database error retrieving user feedback: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        log_error(f"Unexpected error retrieving user feedback: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
