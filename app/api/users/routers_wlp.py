import os
from datetime import datetime, timed<PERSON>ta

from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Body
from fastapi.responses import RedirectResponse, HTMLResponse
from fastapi.security import OAuth2PasswordRequestForm
from starlette.requests import Request
from sqlalchemy.ext.asyncio import AsyncSession

import json

from schemas import schemas_platform
from services import service_user, service_session, crud
from schemas import schemas
from core.errors import AuthorizationError, ValidationError, DatabaseError, DuplicateError, NotFoundError
from core.logger import log_info, log_error, log_warning

from ldap3 import Server, Connection, SIMPLE, SYNC, ALL

import core.config as config
config.get_config()

BASE_URL = os.environ.get("BASE_URL")
FE_URL = os.environ.get("FE_URL")
# BE_URL = os.environ.get("BE_URL")

EXCLUDE_PATH = os.environ.get("EXCLUDE_PATH")

itemrouter = APIRouter()


@itemrouter.post(f"{BASE_URL}/users/wlp/create", tags=["users-wlp"])
async def add_wlp_user(
    body: schemas_platform.UserWLPRequest = Body(...),
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user)
):
    # Check authorization
    if "admin" not in current_user.get("role", []):
        log_warning(
            f"Unauthorized attempt to create user by {current_user.get('username')}",
            context="add_wlp_user"
        )
        raise AuthorizationError("Only admin users can create new users")

    try:
        # Validate username
        if not body.username or len(body.username.strip()) == 0:
            raise ValidationError("Username cannot be empty")

        # Check if user already exists
        existing_user = await crud.get_user_by_platform(session, platform='wlp', username=body.username)
        if existing_user:
            log_warning(
                f"Attempt to create duplicate user '{body.username}' by {current_user.get('username')}",
                context="add_wlp_user"
            )
            raise DuplicateError(
                f"User with username '{body.username}' already exists")

        # Create user
        result = await crud.create_zds_platform_user(session, platform='wlp', **body.dict())
        log_info(
            f"User '{body.username}' created successfully by {current_user.get('username')}",
            context="add_wlp_user"
        )
        return {"message": "User created successfully", "data": result}

    except (ValidationError, AuthorizationError, DuplicateError) as e:
        # These are already handled by our custom exception handler
        raise

    except Exception as e:
        # Log the error here
        log_error(
            e,
            context="add_wlp_user",
            user=current_user.get('username'),
            additional_info={"username": body.username}
        )
        raise DatabaseError(f"Failed to create user: {str(e)}")


@itemrouter.post(f"{BASE_URL}/users/wlp/update", tags=["users-wlp"])
async def update_wlp_user(
    body: schemas_platform.UserWLPRequest = Body(...),
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user)
):
    # Check authorization
    if "admin" not in current_user.get("role", []):
        log_warning(
            f"Unauthorized attempt to update user by {current_user.get('username')}",
            context="update_wlp_user"
        )
        raise AuthorizationError("Only admin users can update users")

    try:
        update_data = {k: v for k, v in body.dict().items() if k !=
                   "username" and v is not None}

        if not update_data:
            raise ValidationError("No fields to update")

        updated_user = await crud.update_zds_platform_user(session, platform='wlp', username=body.username, **update_data)

        if not updated_user:
            raise NotFoundError("User not found")

        return {"message": "User updated successfully", "data": updated_user}

    except (ValidationError, AuthorizationError, NotFoundError) as e:
        # These are already handled by our custom exception handler
        raise

    except Exception as e:
        # Log the error here
        log_error(
            e,
            context="update_wlp_user",
            user=current_user.get('username'),
            additional_info={"username": body.username}
        )
        raise DatabaseError(f"Failed to update user: {str(e)}")
