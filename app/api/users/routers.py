import os
from datetime import datetime, timed<PERSON><PERSON>

from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Body
from fastapi.responses import RedirectResponse, HTMLResponse
from fastapi.security import OAuth2PasswordRequestForm
from starlette.requests import Request
from sqlalchemy.ext.asyncio import AsyncSession

import json

from services import service_user, service_session, crud
from schemas import schemas, schemas_dashboard
from core.errors import AuthorizationError, ValidationError, DatabaseError, DuplicateError, NotFoundError
from core.logger import log_info, log_error, log_warning

from ldap3 import Server, Connection, SIMPLE, SYNC, ALL

import core.config as config
config.get_config()

BASE_URL = os.environ.get("BASE_URL")
FE_URL = os.environ.get("FE_URL")
# BE_URL = os.environ.get("BE_URL")

EXCLUDE_PATH = os.environ.get("EXCLUDE_PATH")

itemrouter = APIRouter()


@itemrouter.get(f"{BASE_URL}/users/register", tags=["users"])
async def register_user(
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: json = Depends(service_user.get_current_active_user),
):
    if current_user['role'] == 'admin':
        return (await crud.create_user(session=session))
    else:
        return False


@itemrouter.post(f"{BASE_URL}/users/update", tags=["users"])
async def update_user(
    body: schemas.UserUpdateRequest = Body(...),
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user)
):
    if "admin" not in current_user.get("role", []):
        log_warning(
            f"Unauthorized attempt to update user by {current_user.get('username')}",
            context="update_wlp_user"
        )
        raise AuthorizationError("Only admin users can update users")

    try:
        update_data = {k: v for k, v in body.dict().items() if k !=
                       "username" and v is not None}

        if not update_data:
            raise ValidationError("No fields to update")

        updated_user = await crud.update_user(session, body.username, **update_data)

        if not updated_user:
            raise NotFoundError("User not found")

        return {"message": "User updated successfully", "user": updated_user}

    except (ValidationError, AuthorizationError, DuplicateError) as e:
        # These are already handled by our custom exception handler
        raise

    except Exception as e:
        # Log the error here
        log_error(
            e,
            context="update_user",
            user=current_user.get('username'),
            additional_info={"username": body.username}
        )
        raise DatabaseError(f"Failed to update user: {str(e)}")


@itemrouter.get(f"{BASE_URL}/users/get_all", tags=["users"])
async def get_all_users_by_platform(
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: json = Depends(service_user.get_current_active_user),
    platform: str = "dataquest",
):
    if current_user['role'] == 'admin':
        return (await crud.get_user_by_platform(session=session, platform=platform))
    else:
        raise AuthorizationError("Only admin users can get all users")
    
@itemrouter.get(f"{BASE_URL}/users/get_basic", tags=["users"])
async def get_all_users_by_platform(
    role: str = 'all',
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: json = Depends(service_user.get_current_active_user),
):
    try:
        return (await crud.get_list_user_basic(session=session, role=role))
    except Exception as e:
        raise DatabaseError(f"Failed to get users: {str(e)}")


@itemrouter.get(f"{BASE_URL}/users/get_all_platform", tags=["users"])
async def get_user_all_platform(
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: json = Depends(service_user.get_current_active_user),
    username: str = 'all',
):
    if current_user['role'] == 'admin':
        data = []
        for platform in ('dataquest', 'wlp', 'ads', 'ecommerce', 'sbv'):
            users = await crud.get_user_by_platform(session=session, platform=platform, username=username)
            if users:
                data.append({
                    'platform': platform,
                    'users': users
                })
        return data
    else:
        raise AuthorizationError("Only admin users can get all users")


@itemrouter.get(f"{BASE_URL}/users/get", tags=["users"])
async def get_user(
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: json = Depends(service_user.get_current_active_user),
):
    data = []
    for platform in ('dataquest', 'wlp', 'ads', 'ecommerce', 'sbv'):
        users = await crud.get_user_by_platform(session=session, platform=platform, username=current_user.username)
        if users:
            data.append({
                'platform': platform,
                'users': users
            })
    return data


@itemrouter.get(f"{BASE_URL}/dashboard", response_model=schemas_dashboard.DashboardResponse, tags=["dashboard"])
async def get_dashboard(
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user)
):
    """
    Get dashboard data for the current user including:
    1. Summary statistics (pending, approved, in-progress requests, etc.)
    2. Recent activity (latest requests by modified date)
    3. My access (platform access, atlas reports, adhoc data)
    """
    try:
        dashboard_data = await crud.get_dashboard_data(session, current_user.get("id"))
        return dashboard_data
    except DatabaseError as e:
        raise HTTPException(status_code=500, detail=str(e))
