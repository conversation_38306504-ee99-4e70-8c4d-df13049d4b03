import os

from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException, Body
from sqlalchemy.ext.asyncio import AsyncSession

import json

from schemas import schemas_noti
from services import service_user, service_session, crud_noti as crud
from core.errors import DatabaseError
from core.logger import log_info, log_error, log_warning


import core.config as config
config.get_config()

BASE_URL = os.environ.get("BASE_URL")
FE_URL = os.environ.get("FE_URL")
# BE_URL = os.environ.get("BE_URL")

EXCLUDE_PATH = os.environ.get("EXCLUDE_PATH")

itemrouter = APIRouter()


@itemrouter.post(f"{BASE_URL}/notifications/create", tags=["notifications"])
async def add_notification(
    body: schemas_noti.NotificationCreate = Body(...),
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user)
):
    try:
        await crud.create_notification(session, body.user_id, body.title, body.content, body.type)
        return {"message": "Notification created successfully"}
    except DatabaseError as e:
        raise HTTPException(status_code=500, detail=str(e))

@itemrouter.get(f"{BASE_URL}/notifications/get", tags=["notifications"])
async def get_notifications(
    user_id: int,
    session: AsyncSession = Depends(service_session.get_db_session)
):
    try:
        notifications = await crud.get_user_notifications(session, user_id)
        return notifications
    except DatabaseError as e:
        raise HTTPException(status_code=500, detail=str(e))

@itemrouter.post(f"{BASE_URL}/notifications/mark-as-read", tags=["notifications"])
async def mark_as_read(
    notification_id: int,
    session: AsyncSession = Depends(service_session.get_db_session)
):
    try:
        await crud.mark_as_read(session, notification_id)
        return {"message": "Notification marked as read successfully"}
    except DatabaseError as e:
        raise HTTPException(status_code=500, detail=str(e))
