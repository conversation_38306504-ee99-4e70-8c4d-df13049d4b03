"""
API routes for Atlas Workbooks and Views
"""

import os
from fastapi import APIRouter, Depends, HTTPException, Body
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional

from schemas import schemas_atlas
from services import service_user, service_session, crud_atlas
from core.errors import DatabaseError, AuthorizationError
from core.logger import log_info, log_error, log_warning

import core.config as config
config.get_config()

BASE_URL = os.environ.get("BASE_URL")
FE_URL = os.environ.get("FE_URL")

itemrouter = APIRouter()


# Atlas Workbooks endpoints

@itemrouter.get(
    f"{BASE_URL}/atlas/owners", 
    response_model=List[schemas_atlas.AtlasWorkbookOwner], 
    tags=["atlas"]
)
async def get_report_owners(
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user)
):
    """Get all report owners"""
    try:
        owners = await crud_atlas.get_report_owners(session)
        return owners
    except DatabaseError as e:
        log_error(f"Error retrieving report owners: {str(e)}", context="get_report_owners")
        raise HTTPException(status_code=500, detail=str(e))


@itemrouter.get(
    f"{BASE_URL}/atlas/workbooks", 
    response_model=List[schemas_atlas.AtlasWorkbookInDB], 
    tags=["atlas"]
)
async def get_all_workbooks(
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user),
    page: int = 1,
    limit: int = 10,
    owner: Optional[str] = None,
    search: Optional[str] = None
):
    """Get all Atlas workbooks"""
    try:
        workbooks = await crud_atlas.get_all_workbooks(session, page, limit, owner, search)
        return workbooks
    except DatabaseError as e:
        log_error(f"Error retrieving workbooks: {str(e)}", context="get_all_workbooks")
        raise HTTPException(status_code=500, detail=str(e))


@itemrouter.get(
    f"{BASE_URL}/atlas/workbooks/{{workbook_id}}", 
    response_model=schemas_atlas.AtlasWorkbookInDB, 
    tags=["atlas"]
)
async def get_workbook(
    workbook_id: int,
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user)
):
    """Get a specific Atlas workbook by ID"""
    try:
        workbook = await crud_atlas.get_workbook_by_id(session, workbook_id)
        if not workbook:
            raise HTTPException(status_code=404, detail=f"Workbook with ID {workbook_id} not found")
        return workbook
    except DatabaseError as e:
        log_error(f"Error retrieving workbook {workbook_id}: {str(e)}", context="get_workbook")
        raise HTTPException(status_code=500, detail=str(e))


@itemrouter.get(
    f"{BASE_URL}/atlas/workbooks/{{workbook_id}}/views", 
    response_model=schemas_atlas.AtlasWorkbookWithViews, 
    tags=["atlas"]
)
async def get_workbook_with_views(
    workbook_id: int,
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user)
):
    """Get a workbook with all its views"""
    try:
        workbook = await crud_atlas.get_workbook_with_views(session, workbook_id)
        if not workbook:
            raise HTTPException(status_code=404, detail=f"Workbook with ID {workbook_id} not found")
        return workbook
    except DatabaseError as e:
        log_error(f"Error retrieving workbook with views {workbook_id}: {str(e)}", context="get_workbook_with_views")
        raise HTTPException(status_code=500, detail=str(e))


@itemrouter.post(
    f"{BASE_URL}/atlas/workbooks", 
    response_model=schemas_atlas.AtlasWorkbookInDB, 
    tags=["atlas"]
)
async def create_workbook(
    workbook: schemas_atlas.AtlasWorkbookCreate = Body(...),
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user)
):
    """Create a new Atlas workbook"""
    # Check if user has admin rights
    if current_user.get("role") != "admin":
        log_warning(
            f"Unauthorized attempt to create workbook by {current_user.get('username')}",
            context="create_workbook"
        )
        raise AuthorizationError("Only admin users can create workbooks")
    
    try:
        new_workbook = await crud_atlas.create_workbook(session, workbook)
        log_info(f"Workbook created: {new_workbook['id']}", context="create_workbook")
        return new_workbook
    except DatabaseError as e:
        log_error(f"Error creating workbook: {str(e)}", context="create_workbook")
        raise HTTPException(status_code=500, detail=str(e))


@itemrouter.put(
    f"{BASE_URL}/atlas/workbooks/{{workbook_id}}", 
    response_model=schemas_atlas.AtlasWorkbookInDB, 
    tags=["atlas"]
)
async def update_workbook(
    workbook_id: int,
    workbook: schemas_atlas.AtlasWorkbookUpdate = Body(...),
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user)
):
    """Update an Atlas workbook"""
    # Check if user has admin rights
    if current_user.get("role") != "admin":
        log_warning(
            f"Unauthorized attempt to update workbook by {current_user.get('username')}",
            context="update_workbook"
        )
        raise AuthorizationError("Only admin users can update workbooks")
    
    try:
        # Check if workbook exists
        existing_workbook = await crud_atlas.get_workbook_by_id(session, workbook_id)
        if not existing_workbook:
            raise HTTPException(status_code=404, detail=f"Workbook with ID {workbook_id} not found")
        
        updated_workbook = await crud_atlas.update_workbook(session, workbook_id, workbook)
        log_info(f"Workbook updated: {workbook_id}", context="update_workbook")
        return updated_workbook
    except DatabaseError as e:
        log_error(f"Error updating workbook {workbook_id}: {str(e)}", context="update_workbook")
        raise HTTPException(status_code=500, detail=str(e))


@itemrouter.delete(
    f"{BASE_URL}/atlas/workbooks/{{workbook_id}}", 
    tags=["atlas"]
)
async def delete_workbook(
    workbook_id: int,
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user)
):
    """Delete an Atlas workbook"""
    # Check if user has admin rights
    if current_user.get("role") != "admin":
        log_warning(
            f"Unauthorized attempt to delete workbook by {current_user.get('username')}",
            context="delete_workbook"
        )
        raise AuthorizationError("Only admin users can delete workbooks")
    
    try:
        # Check if workbook exists
        existing_workbook = await crud_atlas.get_workbook_by_id(session, workbook_id)
        if not existing_workbook:
            raise HTTPException(status_code=404, detail=f"Workbook with ID {workbook_id} not found")
        
        success = await crud_atlas.delete_workbook(session, workbook_id)
        if success:
            log_info(f"Workbook deleted: {workbook_id}", context="delete_workbook")
            return {"message": f"Workbook with ID {workbook_id} deleted successfully"}
        else:
            raise HTTPException(status_code=500, detail=f"Failed to delete workbook with ID {workbook_id}")
    except DatabaseError as e:
        log_error(f"Error deleting workbook {workbook_id}: {str(e)}", context="delete_workbook")
        raise HTTPException(status_code=500, detail=str(e))


# Atlas Views endpoints

@itemrouter.get(
    f"{BASE_URL}/atlas/views", 
    response_model=List[schemas_atlas.AtlasViewInDB], 
    tags=["atlas"]
)
async def get_all_views(
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user),
    page: int = 1,
    limit: int = 10,
    owner: Optional[str] = None,
    search: Optional[str] = None
):
    """Get all Atlas views"""
    try:
        views = await crud_atlas.get_all_views(session, page, limit, owner, search)
        return views
    except DatabaseError as e:
        log_error(f"Error retrieving views: {str(e)}", context="get_all_views")
        raise HTTPException(status_code=500, detail=str(e))


@itemrouter.get(
    f"{BASE_URL}/atlas/views/{{view_id}}", 
    response_model=schemas_atlas.AtlasViewInDB, 
    tags=["atlas"]
)
async def get_view(
    view_id: int,
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user)
):
    """Get a specific Atlas view by ID"""
    try:
        view = await crud_atlas.get_view_by_id(session, view_id)
        if not view:
            raise HTTPException(status_code=404, detail=f"View with ID {view_id} not found")
        return view
    except DatabaseError as e:
        log_error(f"Error retrieving view {view_id}: {str(e)}", context="get_view")
        raise HTTPException(status_code=500, detail=str(e))


@itemrouter.post(
    f"{BASE_URL}/atlas/views", 
    response_model=schemas_atlas.AtlasViewInDB, 
    tags=["atlas"]
)
async def create_view(
    view: schemas_atlas.AtlasViewCreate = Body(...),
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user)
):
    """Create a new Atlas view"""
    # Check if user has admin rights
    if current_user.get("role") != "admin":
        log_warning(
            f"Unauthorized attempt to create view by {current_user.get('username')}",
            context="create_view"
        )
        raise AuthorizationError("Only admin users can create views")
    
    try:
        # If workbook_id is provided, check if it exists
        if view.workbook_id:
            workbook = await crud_atlas.get_workbook_by_id(session, view.workbook_id)
            if not workbook:
                raise HTTPException(status_code=404, detail=f"Workbook with ID {view.workbook_id} not found")
        
        new_view = await crud_atlas.create_view(session, view)
        log_info(f"View created: {new_view['id']}", context="create_view")
        return new_view
    except DatabaseError as e:
        log_error(f"Error creating view: {str(e)}", context="create_view")
        raise HTTPException(status_code=500, detail=str(e))


@itemrouter.put(
    f"{BASE_URL}/atlas/views/{{view_id}}", 
    response_model=schemas_atlas.AtlasViewInDB, 
    tags=["atlas"]
)
async def update_view(
    view_id: int,
    view: schemas_atlas.AtlasViewUpdate = Body(...),
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user)
):
    """Update an Atlas view"""
    # Check if user has admin rights
    if current_user.get("role") != "admin":
        log_warning(
            f"Unauthorized attempt to update view by {current_user.get('username')}",
            context="update_view"
        )
        raise AuthorizationError("Only admin users can update views")
    
    try:
        # Check if view exists
        existing_view = await crud_atlas.get_view_by_id(session, view_id)
        if not existing_view:
            raise HTTPException(status_code=404, detail=f"View with ID {view_id} not found")
        
        # If workbook_id is being updated, check if the new workbook exists
        if view.workbook_id is not None and view.workbook_id != existing_view["workbook_id"]:
            workbook = await crud_atlas.get_workbook_by_id(session, view.workbook_id)
            if not workbook:
                raise HTTPException(status_code=404, detail=f"Workbook with ID {view.workbook_id} not found")
        
        updated_view = await crud_atlas.update_view(session, view_id, view)
        log_info(f"View updated: {view_id}", context="update_view")
        return updated_view
    except DatabaseError as e:
        log_error(f"Error updating view {view_id}: {str(e)}", context="update_view")
        raise HTTPException(status_code=500, detail=str(e))


@itemrouter.delete(
    f"{BASE_URL}/atlas/views/{{view_id}}", 
    tags=["atlas"]
)
async def delete_view(
    view_id: int,
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user)
):
    """Delete an Atlas view"""
    # Check if user has admin rights
    if current_user.get("role") != "admin":
        log_warning(
            f"Unauthorized attempt to delete view by {current_user.get('username')}",
            context="delete_view"
        )
        raise AuthorizationError("Only admin users can delete views")
    
    try:
        # Check if view exists
        existing_view = await crud_atlas.get_view_by_id(session, view_id)
        if not existing_view:
            raise HTTPException(status_code=404, detail=f"View with ID {view_id} not found")
        
        success = await crud_atlas.delete_view(session, view_id)
        if success:
            log_info(f"View deleted: {view_id}", context="delete_view")
            return {"message": f"View with ID {view_id} deleted successfully"}
        else:
            raise HTTPException(status_code=500, detail=f"Failed to delete view with ID {view_id}")
    except DatabaseError as e:
        log_error(f"Error deleting view {view_id}: {str(e)}", context="delete_view")
        raise HTTPException(status_code=500, detail=str(e))
