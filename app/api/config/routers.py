import os
from datetime import datetime, timed<PERSON><PERSON>

from typing import Optional, List
from fastapi import APIRouter, Depends, HTTPException, status, Body, Path, Query
from fastapi.responses import RedirectResponse, HTMLResponse
from fastapi.security import OAuth2PasswordRequestForm
from starlette.requests import Request
from sqlalchemy.ext.asyncio import AsyncSession

import json

from schemas import schemas_platform, schemas_approval
from services import service_user, service_session, crud_config
from schemas import schemas
from core.errors import AuthorizationError, ValidationError, DatabaseError, DuplicateError, NotFoundError
from core.logger import log_info, log_error, log_warning

from ldap3 import Server, Connection, SIMPLE, SYNC, ALL

import core.config as config
config.get_config()

BASE_URL = os.environ.get("BASE_URL")
FE_URL = os.environ.get("FE_URL")
# BE_URL = os.environ.get("BE_URL")

EXCLUDE_PATH = os.environ.get("EXCLUDE_PATH")

itemrouter = APIRouter()


@itemrouter.get(f"{BASE_URL}/config/approval/get", tags=["approval-config"])
async def get_approval_config(
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: json = Depends(service_user.get_current_active_user),
):
    return (await crud_config.get_approval_config(session=session))


# @itemrouter.get(f"{BASE_URL}/config/approval", tags=["approval-config"])
# async def get_approval_config_by_id(
#     config_id: int = Path(..., description="The ID of the approval config to retrieve"),
#     session: AsyncSession = Depends(service_session.get_db_session),
#     current_user: dict = Depends(service_user.get_current_active_user)
# ):
#     """Get a specific approval configuration by ID"""
#     if current_user.get('role') != 'admin':
#         log_warning(
#             f"Unauthorized attempt to get approval config by {current_user.get('username')}",
#             context="get_approval_config_by_id"
#         )
#         raise AuthorizationError("Only admin users can view approval configs")

#     try:
#         return await crud_config.get_approval_config_by_id(session, config_id)
#     except NotFoundError as e:
#         raise HTTPException(status_code=404, detail=str(e))
#     except DatabaseError as e:
#         log_error(
#             e,
#             context="get_approval_config_by_id",
#             user=current_user.get('username'),
#             additional_info={"config_id": config_id}
#         )
#         raise HTTPException(status_code=500, detail=str(e))


@itemrouter.post(f"{BASE_URL}/config/approval/create", tags=["approval-config"])
async def create_approval_config(
    body: schemas_approval.ApprovalConfigCreate = Body(...),
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user)
):
    """Create a new approval configuration"""
    if current_user.get('role') != 'admin':
        log_warning(
            f"Unauthorized attempt to create approval config by {current_user.get('username')}",
            context="create_approval_config"
        )
        raise AuthorizationError("Only admin users can create approval configs")

    try:
        config = await crud_config.create_approval_config(session, body.dict())
        return {"message": "Approval config created successfully", "config": config}
    except DatabaseError as e:
        log_error(
            e,
            context="create_approval_config",
            user=current_user.get('username'),
            additional_info={"config_data": body.dict()}
        )
        raise HTTPException(status_code=500, detail=str(e))


# @itemrouter.post(f"{BASE_URL}/config/approval/bulk-create", tags=["approval-config"])
# async def create_approval_configs_bulk(
#     body: schemas_approval.ApprovalConfigBulkCreate = Body(...),
#     session: AsyncSession = Depends(service_session.get_db_session),
#     current_user: dict = Depends(service_user.get_current_active_user)
# ):
#     """Create multiple approval configurations in bulk"""
#     if current_user.get('role') != 'admin':
#         log_warning(
#             f"Unauthorized attempt to bulk create approval configs by {current_user.get('username')}",
#             context="create_approval_configs_bulk"
#         )
#         raise AuthorizationError("Only admin users can create approval configs")

#     try:
#         configs = [config.dict() for config in body.configs]
#         results = await crud_config.create_approval_configs_bulk(session, configs)
#         return {"message": f"{len(results)} approval configs created successfully", "configs": results}
#     except DatabaseError as e:
#         log_error(
#             e,
#             context="create_approval_configs_bulk",
#             user=current_user.get('username')
#         )
#         raise HTTPException(status_code=500, detail=str(e))


# @itemrouter.post(f"{BASE_URL}/config/approval/update", tags=["approval-config"])
# async def update_approval_config(
#     config_id: int = Path(..., description="The ID of the approval config to update"),
#     body: schemas_approval.ApprovalConfigUpdate = Body(...),
#     session: AsyncSession = Depends(service_session.get_db_session),
#     current_user: dict = Depends(service_user.get_current_active_user)
# ):
#     """Update an existing approval configuration"""
#     if current_user.get('role') != 'admin':
#         log_warning(
#             f"Unauthorized attempt to update approval config by {current_user.get('username')}",
#             context="update_approval_config"
#         )
#         raise AuthorizationError("Only admin users can update approval configs")

#     try:
#         # Filter out None values
#         update_data = {k: v for k, v in body.dict().items() if v is not None}

#         if not update_data:
#             raise ValidationError("No fields to update")

#         config = await crud_config.update_approval_config(session, config_id, **update_data)
#         return {"message": "Approval config updated successfully", "config": config}
#     except NotFoundError as e:
#         raise HTTPException(status_code=404, detail=str(e))
#     except ValidationError as e:
#         raise HTTPException(status_code=400, detail=str(e))
#     except DatabaseError as e:
#         log_error(
#             e,
#             context="update_approval_config",
#             user=current_user.get('username'),
#             additional_info={"config_id": config_id, "update_data": body.dict()}
#         )
#         raise HTTPException(status_code=500, detail=str(e))


@itemrouter.post(f"{BASE_URL}/config/approval/delete", tags=["approval-config"])
async def delete_approval_config(
    config_id: int,
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user)
):
    """Delete an approval configuration"""
    if current_user.get('role') != 'admin':
        log_warning(
            f"Unauthorized attempt to delete approval config by {current_user.get('username')}",
            context="delete_approval_config"
        )
        raise AuthorizationError("Only admin users can delete approval configs")

    try:
        config = await crud_config.delete_approval_config(session, config_id)
        return {"message": "Approval config deleted successfully", "config": config}
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except DatabaseError as e:
        log_error(
            e,
            context="delete_approval_config",
            user=current_user.get('username'),
            additional_info={"config_id": config_id}
        )
        raise HTTPException(status_code=500, detail=str(e))


# @itemrouter.get(f"{BASE_URL}/users/get_all", tags=["users"])
# async def get_all_users_by_platform(
#     session: AsyncSession = Depends(service_session.get_db_session),
#     current_user: json = Depends(service_user.get_current_active_user),
#     platform: str = "dataquest",
# ):
#     if current_user['role'] == 'admin':
#         return (await crud.get_user_by_platform(session=session, platform=platform))
#     else:
#         raise AuthorizationError("Only admin users can get all users")


# @itemrouter.get(f"{BASE_URL}/users/get_all_platform", tags=["users"])
# async def get_user_all_platform(
#     session: AsyncSession = Depends(service_session.get_db_session),
#     current_user: json = Depends(service_user.get_current_active_user),
#     username: str = 'all',
# ):
#     if current_user['role'] == 'admin':
#         data = []
#         for platform in ('dataquest', 'wlp', 'ads', 'ecommerce', 'sbv'):
#             users = await crud.get_user_by_platform(session=session, platform=platform, username=username)
#             if users:
#                 data.append({
#                     'platform': platform,
#                     'users': users
#                 })
#         return data
#     else:
#         raise AuthorizationError("Only admin users can get all users")


# @itemrouter.get(f"{BASE_URL}/users/get", tags=["users"])
# async def get_user(
#     session: AsyncSession = Depends(service_session.get_db_session),
#     current_user: json = Depends(service_user.get_current_active_user),
# ):
#     data = []
#     for platform in ('dataquest', 'wlp', 'ads', 'ecommerce', 'sbv'):
#         users = await crud.get_user_by_platform(session=session, platform=platform, username=current_user.username)
#         if users:
#             data.append({
#                 'platform': platform,
#                 'users': users
#             })
#     return data
