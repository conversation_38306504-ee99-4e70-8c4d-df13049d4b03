import os
from datetime import datetime, timed<PERSON>ta

from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Body
from fastapi.responses import RedirectResponse, HTMLResponse
from fastapi.security import OAuth2PasswordRequestForm
from starlette.requests import Request
from sqlalchemy.ext.asyncio import AsyncSession

import json

from services import service_user, service_session, crud
from schemas import schemas

# from ldap3 import Server, Connection, SIMPLE, SYNC, ALL
import ldap3

import core.config as config
config.get_config()

BASE_URL = os.environ.get("BASE_URL")
FE_URL = os.environ.get("FE_URL")
# BE_URL = os.environ.get("BE_URL")

EXCLUDE_PATH = os.environ.get("EXCLUDE_PATH")

itemrouter = APIRouter()

# Cấu hình LDAP
LDAP_SERVER = "ldaps://ldap.vng.com.vn:636"
BASE_DN = "ou=People,dc=vng,dc=com,dc=vn"
SERVICE_DN = "cn=zds.wl"
SERVICE_PASSWORD = "2c018d791f5c72a2e7de07113937a881bda54689"

@itemrouter.get(f"{BASE_URL}/hello", tags=["test"])
async def hello():
    return {"message": "Hello World"}

# LDAP VNG LOGIN
# if user is not exist in database, create new user
@itemrouter.post(f"{BASE_URL}/auth/ldaplogin", tags=["auth"])
async def vngldap_authenticate(
    form_data: schemas.LoginRequest, session: AsyncSession = Depends(service_session.get_db_session)
):
    # for test
    if form_data.username == 'approval' or form_data.username == 'user' or form_data.username == 'admin':
        data = {
            "username": form_data.username,
            "display_name": form_data.username
        }
        user_data = await service_user.authenticate_user(session, data)
        user_verify = user_data['data']

        access_token_expires = timedelta(
            minutes=service_user.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = await service_user.create_access_token(
            data={
                "username": data["username"],
                "displayName": data["display_name"],
                "vng_token_id": "",
                "vng_token_expired": "",
            },
            expires_delta=access_token_expires,
        )
        # return {"status": 1, "access_token": access_token, "type": user_data['type']}
        return {"status": 1, "access_token": access_token, "type": "new"}

    print(">>>>>>>>>>>>>>>>> LDAP LOGIN")
    # Khởi tạo server
    server = ldap3.Server(
        LDAP_SERVER,
        use_ssl=True,
        get_info=ldap3.ALL
    )
    
    try:
        # Bind với service account
        service_conn = ldap3.Connection(
            server,
            user=SERVICE_DN,
            password=SERVICE_PASSWORD,
            authentication=ldap3.SIMPLE,
            client_strategy=ldap3.SYNC,
            raise_exceptions=False,
        )

        if not service_conn.bind():
            return {"status": -1, "message": "Service account bind failed"}

        # Search user to get user DN
        try:
            print(">>> Search user to get user DN")
            user_search = service_conn.search(
                BASE_DN,
                f"(uid={form_data.username})",
                attributes=['*']  # Lấy tất cả attributes
            )
            if not user_search or not service_conn.entries:
                return {"status": 0, "message": "User not found. Please check your username and try again."}
        except ldap3.core.exceptions.LDAPException as e:
            return {"status": 0, "message": "User not found. Please check your username and try again."}

        user_entry = service_conn.entries[0]
        user_dn = user_entry.entry_dn
        
        # user authentication trước khi lấy thông tin
        print(">>> user authentication")
        user_conn = ldap3.Connection(
            server,
            user=user_dn,
            password=form_data.password,
            auto_bind=False
            # authentication=ldap3.SIMPLE
        )
        
        if not user_conn.bind():
            await crud.gen_app_log(session, form_data.username, "ldaplogin", "error 0", "Invalid Password")
            return {"status": 0, "message": "Invalid password. Please check your password and try again."}

        # Lấy manager DN từ attribute manager
        print(">>> Get manager username")
        manager_dn = user_entry.manager.value if hasattr(user_entry, 'manager') else None
        manager_username = None
        
        # Nếu có manager DN, search để lấy username của manager
        if manager_dn:
            manager_cn = manager_dn.split(',')[0].replace('cn=', '')
            # print(f"Manager CN: {manager_cn}")  # Debug print
            manager_search = service_conn.search(
                BASE_DN,
                f"(cn={manager_cn})",  # Search bằng CN
                attributes=['uid']
            )
            if manager_search and service_conn.entries:
                manager_username = service_conn.entries[0].uid.value
                
        # Tạo user profile với kiểm tra null safety
        # print(user_entry)
        user_profile = {
            "username": user_entry.uid.value if hasattr(user_entry, 'uid') else None,
            "display_name": user_entry.displayName.value if hasattr(user_entry, 'displayName') else None,
            "given_name": user_entry.givenName.value if hasattr(user_entry, 'givenName') else None,
            "surname": user_entry.sn.value if hasattr(user_entry, 'sn') else None,
            "department_code": user_entry.departmentcode.value if hasattr(user_entry, 'departmentcode') else None,
            "department": user_entry.department.value if hasattr(user_entry, 'department') else None,
            "job_title": user_entry.jobtitle.value if hasattr(user_entry, 'jobtitle') else None,
            "manager": manager_username
        }      
                
        user_data = await service_user.authenticate_user(session, user_profile)
        user_verify = user_data['data']

        if not user_verify:
            # raise HTTPException(
            #     status_code=status.HTTP_401_UNAUTHORIZED,
            #     detail="Incorrect username",
            #     headers={"WWW-Authenticate": "Bearer"},
            # )
            await crud.gen_app_log(session, form_data.username, "ldaplogin", "error -1", "can not verify user")
            return {"status": -1, "message": "Can not verify user"}

        access_token_expires = timedelta(
            minutes=service_user.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = await service_user.create_access_token(
            data=user_profile,
            expires_delta=access_token_expires,
        )
        # print("token_id: " + access_token)
        await crud.gen_app_log(session, user_verify.username, "ldaplogin", "success", "")
        return {"status": 1, "access_token": access_token, "type": user_data['type']}

    except Exception as e:
        return {"status": -1, "message": str(e)}
    finally:
        if 'service_conn' in locals():
            service_conn.unbind()
        if 'user_conn' in locals():
            user_conn.unbind()


@itemrouter.get(f"{BASE_URL}/auth/logout", tags=["auth"])
async def logout(request: Request):
    if request.session.pop("user", None):
        return {"status": "logout"}
    else:
        return HTMLResponse("Logout failed", status_code=403)


@itemrouter.get(f"{BASE_URL}/auth/profile", tags=["auth"])
async def get_profile(
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: json = Depends(service_user.get_current_active_user),
):
    return (await crud.get_user_by_username(session=session, username=current_user['username']))
