from sqlalchemy.ext.asyncio import AsyncSession
from fastapi.security import OAuth2<PERSON>asswordBearer
from fastapi import Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from datetime import datetime, timed<PERSON>ta
from typing import Optional
from passlib.context import CryptContext
from jose import JW<PERSON>rror, jwt
from cas import CASClient

import json

from services import crud, service_session

import os

import core.config as config
config.get_config()

BASE_URL = os.environ.get("BASE_URL")
FE_URL = os.environ.get("FE_URL")
BE_URL = os.environ.get("BE_URL")


# openssl rand -hex 32
SECRET_KEY = "a87059633e39ef9d71a06a0dd1e2995b6fea1080dfe4be6eaeb3d094902f02aa"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 1440

cas_client = CASClient(
    version=3,
    service_url=f"{BE_URL}{BASE_URL}/login?next=%2Fauth/login",
    server_url="https://login.vng.com.vn/sso/login",
)

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

async def gen_pass(username:str, password:str):
    your_pass = pwd_context.hash(password)
    # print(f"{username}'s password is : {your_pass}")
    return your_pass

async def verify_password(plain_password, hashed_password):
    # print(f">>> {plain_password}, {hashed_password}, {pwd_context.verify(plain_password, hashed_password)}")
    return pwd_context.verify(plain_password, hashed_password)

async def authenticate_user(session: AsyncSession, data: dict):
    try:
        user = await crud.get_user_by_username(session, username=data['username'])
        # print(">>>>>> ", user)
        if not user:
            # print(">>>>>> Chưa tồn tại user:", data['username'])
            # role_default = "user"
            # user = await crud.create_user(session, username=data['username'], display_name=data['display_name'], role=role_default)
            user = await crud.create_user(session, **data)
            user_type = 'new'
            # print(">>>>>> Đã tạo user mới:", user)
        else:
            # print(">>>>>> Kiểm tra thay đổi thông tin của user:")

            # Tạo dictionary chứa các trường cần update
            update_fields = {}

            # Kiểm tra từng trường và chỉ thêm vào update_fields nếu có thay đổi
            if data['display_name'] and data['display_name'] != user.display_name:
                update_fields['display_name'] = data['display_name']

            if 'given_name' in data and data['given_name'] != getattr(user, 'given_name', None):
                update_fields['given_name'] = data['given_name']

            if 'given_name' in data and data['surname'] != getattr(user, 'surname', None):
                update_fields['surname'] = data['surname']

            if 'department_code' in data and data['department_code'] != getattr(user, 'department_code', None):
                update_fields['department_code'] = data['department_code']

            if 'department' in data and data['department'] != getattr(user, 'department', None):
                update_fields['department'] = data['department']

            if 'job_title' in data and data['job_title'] != getattr(user, 'job_title', None):
                update_fields['job_title'] = data['job_title']

            if 'manager' in data and data['manager'] != getattr(user, 'manager', None):
                update_fields['manager'] = data['manager']

            # Chỉ gọi update_user nếu có ít nhất một trường cần cập nhật
            if update_fields:
                # print(">>>>>> Update user với các trường:", update_fields)
                user = await crud.update_user(session, username=data['username'], **update_fields)
                user_type = 'update'
            else:
                user_type = 'no-change'
                # print(">>>>>> Không có trường nào thay đổi, không cần update")

        return {"data": user, "type": user_type}
    except Exception as e:
        print(">>>>>> Error:", e)
        return False


# async def authenticate_user_pass(session: AsyncSession, username: str, password: str):
#     user = await crud.get_user_by_username(session, username=username)
#     # print(">>>>>> ", user)
#     if not user:
#         return False
#     if not (await verify_password(password, user.hashed_password)):
#         return False
#     return user


async def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


async def get_current_user(
    session: AsyncSession = Depends(service_session.get_db_session), token: str = Depends(oauth2_scheme)
):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "BWearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        # print(payload)
        username: str = payload.get("username")
        if username is None:
            raise credentials_exception
        # token_data = TokenData(username=username)
    except JWTError:
        raise credentials_exception
    # user = get_user(fake_users_db, username=token_data.username)
    user = await crud.get_user_by_username(session, username=username)
    if user is None:
        raise credentials_exception
    payload["id"] = user.id
    payload["display_name"] = user.display_name
    payload["given_name"] = user.given_name
    payload["role"] = user.role
    payload["status"] = user.status
    payload["manager"] = user.manager
    payload["created_at"] = user.created_at
    return payload


async def get_current_active_user(current_user: json = Depends(get_current_user)):
    if current_user["status"] != "active":
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user


# async def get_ads_active_user(current_user: json = Depends(get_current_active_user)):
#     if list(set(["all", "unilever", "samsung"]) & set(current_user["role"])) == []:
#         raise HTTPException(status_code=400, detail="Don't have permission to access Ads report")
#     return current_user


# async def get_whitelist_active_user(current_user: json = Depends(get_current_active_user)):
#     if "all" not in current_user["role"] and "whitelist" not in current_user["role"]:
#         raise HTTPException(status_code=400, detail="Don't have permission to access Whitelist Tool")
#     return current_user


# async def get_zns_active_user(current_user: json = Depends(get_current_active_user)):
#     if "all" not in current_user["role"] and "zns" not in current_user["role"]:
#         raise HTTPException(status_code=400, detail="Don't have permission to access ZNS Tool")
#     return current_user


# async def get_funnel_active_user(current_user: json = Depends(get_current_active_user)):
#     if "all" not in current_user["role"] and "funnel" not in current_user["role"]:
#         raise HTTPException(status_code=400, detail="Don't have permission to access Funnel Tool")
#     return current_user


# async def get_funnel_api_active_user(current_user: json = Depends(get_funnel_active_user)):
#     if "funnel_api" not in current_user["role_lvl1"] and current_user["role_lvl1"] != [] and "all" not in current_user["role_lvl1"]:
#         raise HTTPException(status_code=400, detail="Don't have permission to access this Funnel Tool API")
#     return current_user


# async def get_funnel_report_active_user(current_user: json = Depends(get_funnel_active_user)):
#     if "funnel_report" not in current_user["role_lvl1"] and current_user["role_lvl1"] != [] and "all" not in current_user["role_lvl1"]:
#         raise HTTPException(status_code=400, detail="Don't have permission to access this Funnel Tool API")
#     return current_user


# async def get_funnel_event_active_user(current_user: json = Depends(get_funnel_active_user)):
#     if "funnel_event" not in current_user["role_lvl1"] and current_user["role_lvl1"] != [] and "all" not in current_user["role_lvl1"]:
#         raise HTTPException(status_code=400, detail="Don't have permission to access this Funnel Tool API")
#     return current_user
