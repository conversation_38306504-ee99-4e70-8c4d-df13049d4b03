from sqlalchemy.future import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text, insert, update, delete, func
from datetime import datetime
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from sqlalchemy.orm import aliased
import json

from models import models
from core.errors import DatabaseError, NotFoundError, DuplicateError, ValidationError


async def get_user_by_username(session: AsyncSession, username: str):
    try:
        if username == 'all':
            result = (await session.execute(select(models.users))).all()
        else:
            result = (await session.execute(select(models.users).filter(models.users.c.username == username))).first()
        return result
    except SQLAlchemyError as e:
        raise DatabaseError(f"Error retrieving user: {str(e)}")

async def get_list_user_basic(session: AsyncSession, role: str = 'all'):
    try:
        if role != 'all':
            result = (await session.execute(select(models.users.c.id, models.users.c.username, models.users.c.display_name).filter(models.users.c.role == role))).all()
        else:
            result = (await session.execute(select(models.users.c.id, models.users.c.username, models.users.c.display_name))).all()
        return result
    except SQLAlchemyError as e:
        raise DatabaseError(f"Error retrieving user: {str(e)}")

# async def get_users_wlp(session: AsyncSession, username: str = 'all'):
#     try:
#         if username == 'all':
#             result = (await session.execute(select(models.users_wlp))).mappings().all()
#         else:
#             result = (await session.execute(select(models.users_wlp).filter(models.users_wlp.c.username == username))).mappings().all()
#         return result
#     except SQLAlchemyError as e:
#         raise DatabaseError(f"Error retrieving WLP users: {str(e)}")


async def get_user_by_platform(session: AsyncSession, platform: str, username: str = 'all'):
    try:
        if username == 'all':
            if platform == 'dataquest':
                result = (await session.execute(select(models.users))).mappings().all()
            elif platform == 'wlp':
                result = (await session.execute(select(models.users_wlp))).mappings().all()
            elif platform == 'ads':
                result = (await session.execute(select(models.users_ads))).mappings().all()
            elif platform == 'sbv':
                result = (await session.execute(select(models.users_sbv))).mappings().all()
            elif platform == 'ecommerce':
                result = (await session.execute(select(models.users_ecommerce))).mappings().all()
            else:
                raise ValidationError(f"Invalid platform: {platform}")
        else:
            if platform == 'dataquest':
                result = (await session.execute(select(models.users).filter(models.users.c.username == username))).mappings().first()
            elif platform == 'wlp':
                result = (await session.execute(select(models.users_wlp).filter(models.users_wlp.c.username == username))).mappings().first()
            elif platform == 'ads':
                result = (await session.execute(select(models.users_ads).filter(models.users_ads.c.username == username))).mappings().first()
            elif platform == 'sbv':
                result = (await session.execute(select(models.users_sbv).filter(models.users_sbv.c.username == username))).mappings().first()
            elif platform == 'ecommerce':
                result = (await session.execute(select(models.users_ecommerce).filter(models.users_ecommerce.c.username == username))).mappings().first()
        return result
    except SQLAlchemyError as e:
        raise DatabaseError(f"Error retrieving users by platform: {str(e)}")

# Register new user for DataQuest Platform


async def create_user(session: AsyncSession, username: str, display_name: str, role: list):
    try:
        stmt = (
            insert(models.users)
            .values(username=username, display_name=display_name, role=role)
            .returning(*models.users.c)  # Trả về tất cả các cột
        )
        result = await session.execute(stmt)
        print("1. Request created successfully")

        await session.commit()
        return result.mappings().first()  # Trả về dict chứa toàn bộ dữ liệu user vừa tạo
    except IntegrityError as e:
        if "duplicate key" in str(e).lower():
            raise DuplicateError(
                f"User with username '{username}' already exists")
        raise DatabaseError(f"Database integrity error: {str(e)}")
    except SQLAlchemyError as e:
        raise DatabaseError(f"Error creating user: {str(e)}")


async def update_user(session: AsyncSession, username: str, **kwargs):
    try:
        # Check if user exists
        user = await get_user_by_username(session, username)
        if not user:
            raise NotFoundError(f"User with username '{username}' not found")

        stmt = (
            update(models.users)
            .where(models.users.c.username == username)
            .values(**kwargs)
            # Trả về tất cả các cột sau khi cập nhật
            .returning(*models.users.c)
        )
        result = await session.execute(stmt)
        await session.commit()
        return result.mappings().first()  # Trả về dict chứa dữ liệu user sau khi cập nhật
    except IntegrityError as e:
        raise DatabaseError(f"Database integrity error: {str(e)}")
    except SQLAlchemyError as e:
        raise DatabaseError(f"Error updating user: {str(e)}")


# Add new user for ZDS Platform (WLP, ADS, SBV, Ecommerce,...)
async def create_zds_platform_user(session: AsyncSession, platform: str, **kwargs):
    try:
        if platform == 'wlp':
            stmt = (
                insert(models.users_wlp)
                .values(**kwargs)
                .returning(*models.users_wlp.c)  # Trả về tất cả các cột
            )
        elif platform == 'ads':
            stmt = (
                insert(models.users_ads)
                .values(**kwargs)
                .returning(*models.users_ads.c)  # Trả về tất cả các cột
            )
        elif platform == 'sbv':
            stmt = (
                insert(models.users_sbv)
                .values(**kwargs)
                .returning(*models.users_sbv.c)  # Trả về tất cả các cột
            )
        elif platform == 'ecommerce':
            stmt = (
                insert(models.users_ecommerce)
                .values(**kwargs)
                .returning(*models.users_ecommerce.c)  # Trả về tất cả các cột
            )
        else:
            raise ValidationError(f"Invalid platform: {platform}")

        result = await session.execute(stmt)
        await session.commit()
        return result.mappings().first()  # Trả về dict chứa toàn bộ dữ liệu user vừa tạo
    except IntegrityError as e:
        if "duplicate key" in str(e).lower():
            raise DuplicateError(
                f"User with username '{kwargs.get('username')}' already exists")
        raise DatabaseError(f"Database integrity error: {str(e)}")
    except SQLAlchemyError as e:
        raise DatabaseError(
            f"Error creating {platform.upper()} user: {str(e)}")


async def update_zds_platform_user(session: AsyncSession, platform: str, username: str, **kwargs):
    try:
        if platform == 'wlp':
            stmt = (
                update(models.users_wlp)
                .where(models.users_wlp.c.username == username)
                .values(**kwargs)
                .returning(*models.users_wlp.c)  # Trả về tất cả các cột
            )
        elif platform == 'ads':
            stmt = (
                update(models.users_ads)
                .where(models.users_ads.c.username == username)
                .values(**kwargs)
                .returning(*models.users_ads.c)  # Trả về tất cả các cột
            )
        elif platform == 'sbv':
            stmt = (
                update(models.users_sbv)
                .where(models.users_sbv.c.username == username)
                .values(**kwargs)
                .returning(*models.users_sbv.c)  # Trả về tất cả các cột
            )
        elif platform == 'ecommerce':
            stmt = (
                update(models.users_ecommerce)
                .where(models.users_ecommerce.c.username == username)
                .values(**kwargs)
                .returning(*models.users_ecommerce.c)  # Trả về tất cả các cột
            )
        else:
            raise ValidationError(f"Invalid platform: {platform}")

        result = await session.execute(stmt)
        # print(result.mappings().first())
        await session.commit()
        # Trả về dict chứa toàn bộ dữ liệu user vừa cập nhật
        return result.mappings().first()
    except IntegrityError as e:
        raise DatabaseError(f"Database integrity error: {str(e)}")
    except SQLAlchemyError as e:
        raise DatabaseError(
            f"Error updating {platform.upper()} user: {str(e)}")


async def gen_app_log(session: AsyncSession, username: str, log_type: str, log_content: str, error_log: str):
    try:
        statement = text("""
        INSERT INTO app_logs(username, log_type, log_content, created_at, error_log)
        VALUES(:username, :log_type, :log_content, :created_at, :error_log)
        """)

        print(">>>>> GEN APP LOGS")

        result = await session.execute(statement, {
            "username": username,
            "log_type": log_type,
            "log_content": log_content,
            "created_at": datetime.now(),
            "error_log": error_log
        })

        await session.commit()
        return result
    except SQLAlchemyError as e:
        raise DatabaseError(f"Error generating app log: {str(e)}")