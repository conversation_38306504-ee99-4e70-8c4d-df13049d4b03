from sqlalchemy.future import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text, insert, update, delete
from datetime import datetime
from sqlalchemy.exc import SQLAlchemyError, IntegrityError

from models import models
from core.errors import DatabaseError, NotFoundError, DuplicateError, ValidationError


async def get_approval_config(session: AsyncSession):
    try:
        stmt = (
            select(
                # models.approval_config,
                models.approval_config.c.id,
                models.approval_config.c.request_type,
                models.approval_config.c.platform,
                models.approval_config.c.role,
                models.approval_config.c.user_id,
                models.users.c.username,
                models.users.c.display_name,
            )
            .join(
                models.users,
                models.approval_config.c.user_id == models.users.c.id,
                isouter=True,
            )
        )

        result = (await session.execute(stmt)).all()
        return result
    except SQLAlchemyError as e:
        raise DatabaseError(f"Error retrieving user: {str(e)}")


async def get_approval_config_by_id(session: AsyncSession, config_id: int):
    """Get approval configuration by ID"""
    try:
        result = (await session.execute(
            select(models.approval_config).where(models.approval_config.c.id == config_id)
        )).first()

        if not result:
            raise NotFoundError(f"Approval config with ID {config_id} not found")

        return result
    except NotFoundError:
        raise
    except SQLAlchemyError as e:
        raise DatabaseError(f"Error retrieving approval config: {str(e)}")


async def create_approval_config(session: AsyncSession, config_data: dict):
    """Create a new approval configuration"""
    try:
        stmt = (
            insert(models.approval_config)
            .values(**config_data)
            .returning(*models.approval_config.c)
        )
        result = await session.execute(stmt)
        await session.commit()
        return result.mappings().first()
    except IntegrityError as e:
        raise DatabaseError(f"Database integrity error: {str(e)}")
    except SQLAlchemyError as e:
        raise DatabaseError(f"Error creating approval config: {str(e)}")


async def create_approval_configs_bulk(session: AsyncSession, configs: list):
    """Create multiple approval configurations in bulk"""
    try:
        results = []
        for config_data in configs:
            stmt = (
                insert(models.approval_config)
                .values(**config_data)
                .returning(*models.approval_config.c)
            )
            result = await session.execute(stmt)
            results.append(result.mappings().first())

        await session.commit()
        return results
    except IntegrityError as e:
        await session.rollback()
        raise DatabaseError(f"Database integrity error: {str(e)}")
    except SQLAlchemyError as e:
        await session.rollback()
        raise DatabaseError(f"Error creating approval configs: {str(e)}")


async def update_approval_config(session: AsyncSession, config_id: int, **kwargs):
    """Update an existing approval configuration"""
    try:
        # Check if config exists
        await get_approval_config_by_id(session, config_id)

        stmt = (
            update(models.approval_config)
            .where(models.approval_config.c.id == config_id)
            .values(**kwargs)
            .returning(*models.approval_config.c)
        )
        result = await session.execute(stmt)
        await session.commit()
        return result.mappings().first()
    except NotFoundError:
        raise
    except IntegrityError as e:
        raise DatabaseError(f"Database integrity error: {str(e)}")
    except SQLAlchemyError as e:
        raise DatabaseError(f"Error updating approval config: {str(e)}")


async def delete_approval_config(session: AsyncSession, config_id: int):
    """Delete an approval configuration"""
    try:
        # Check if config exists
        await get_approval_config_by_id(session, config_id)

        stmt = (
            delete(models.approval_config)
            .where(models.approval_config.c.id == config_id)
            .returning(*models.approval_config.c)
        )
        result = await session.execute(stmt)
        await session.commit()
        return result.mappings().first()
    except NotFoundError:
        raise
    except SQLAlchemyError as e:
        raise DatabaseError(f"Error deleting approval config: {str(e)}")


# # async def get_users_wlp(session: AsyncSession, username: str = 'all'):
# #     try:
# #         if username == 'all':
# #             result = (await session.execute(select(models.users_wlp))).mappings().all()
# #         else:
# #             result = (await session.execute(select(models.users_wlp).filter(models.users_wlp.c.username == username))).mappings().all()
# #         return result
# #     except SQLAlchemyError as e:
# #         raise DatabaseError(f"Error retrieving WLP users: {str(e)}")


# async def get_user_by_platform(session: AsyncSession, platform: str, username: str = 'all'):
#     try:
#         if username == 'all':
#             if platform == 'dataquest':
#                 result = (await session.execute(select(models.users))).all()
#             elif platform == 'wlp':
#                 result = (await session.execute(select(models.users_wlp))).mappings().all()
#             elif platform == 'ads':
#                 result = (await session.execute(select(models.users_ads))).mappings().all()
#             elif platform == 'sbv':
#                 result = (await session.execute(select(models.users_sbv))).mappings().all()
#             elif platform == 'ecommerce':
#                 result = (await session.execute(select(models.users_ecommerce))).mappings().all()
#             else:
#                 raise ValidationError(f"Invalid platform: {platform}")
#         else:
#             if platform == 'dataquest':
#                 result = (await session.execute(select(models.users).filter(models.users.c.username == username))).first()
#             elif platform == 'wlp':
#                 result = (await session.execute(select(models.users_wlp).filter(models.users_wlp.c.username == username))).first()
#             elif platform == 'ads':
#                 result = (await session.execute(select(models.users_ads).filter(models.users_ads.c.username == username))).first()
#             elif platform == 'sbv':
#                 result = (await session.execute(select(models.users_sbv).filter(models.users_sbv.c.username == username))).first()
#             elif platform == 'ecommerce':
#                 result = (await session.execute(select(models.users_ecommerce).filter(models.users_ecommerce.c.username == username))).first()
#         return result
#     except SQLAlchemyError as e:
#         raise DatabaseError(f"Error retrieving users by platform: {str(e)}")

# # Register new user for DataQuest Platform


# async def create_user(session: AsyncSession, username: str, display_name: str, role: list):
#     try:
#         stmt = (
#             insert(models.users)
#             .values(username=username, display_name=display_name, role=role)
#             .returning(*models.users.c)  # Trả về tất cả các cột
#         )
#         result = await session.execute(stmt)
#         await session.commit()
#         return result.mappings().first()  # Trả về dict chứa toàn bộ dữ liệu user vừa tạo
#     except IntegrityError as e:
#         if "duplicate key" in str(e).lower():
#             raise DuplicateError(
#                 f"User with username '{username}' already exists")
#         raise DatabaseError(f"Database integrity error: {str(e)}")
#     except SQLAlchemyError as e:
#         raise DatabaseError(f"Error creating user: {str(e)}")



# # Add new user for ZDS Platform (WLP, ADS, SBV, Ecommerce,...)
# async def create_zds_platform_user(session: AsyncSession, platform: str, **kwargs):
#     try:
#         if platform == 'wlp':
#             stmt = (
#                 insert(models.users_wlp)
#                 .values(**kwargs)
#                 .returning(*models.users_wlp.c)  # Trả về tất cả các cột
#             )
#         elif platform == 'ads':
#             stmt = (
#                 insert(models.users_ads)
#                 .values(**kwargs)
#                 .returning(*models.users_ads.c)  # Trả về tất cả các cột
#             )
#         elif platform == 'sbv':
#             stmt = (
#                 insert(models.users_sbv)
#                 .values(**kwargs)
#                 .returning(*models.users_sbv.c)  # Trả về tất cả các cột
#             )
#         elif platform == 'ecommerce':
#             stmt = (
#                 insert(models.users_ecommerce)
#                 .values(**kwargs)
#                 .returning(*models.users_ecommerce.c)  # Trả về tất cả các cột
#             )
#         else:
#             raise ValidationError(f"Invalid platform: {platform}")

#         result = await session.execute(stmt)
#         await session.commit()
#         return result.mappings().first()  # Trả về dict chứa toàn bộ dữ liệu user vừa tạo
#     except IntegrityError as e:
#         if "duplicate key" in str(e).lower():
#             raise DuplicateError(
#                 f"User with username '{kwargs.get('username')}' already exists")
#         raise DatabaseError(f"Database integrity error: {str(e)}")
#     except SQLAlchemyError as e:
#         raise DatabaseError(
#             f"Error creating {platform.upper()} user: {str(e)}")


# async def update_zds_platform_user(session: AsyncSession, platform: str, username: str, **kwargs):
#     try:
#         if platform == 'wlp':
#             stmt = (
#                 update(models.users_wlp)
#                 .where(models.users_wlp.c.username == username)
#                 .values(**kwargs)
#                 .returning(*models.users_wlp.c)  # Trả về tất cả các cột
#             )
#         elif platform == 'ads':
#             stmt = (
#                 update(models.users_ads)
#                 .where(models.users_ads.c.username == username)
#                 .values(**kwargs)
#                 .returning(*models.users_ads.c)  # Trả về tất cả các cột
#             )
#         elif platform == 'sbv':
#             stmt = (
#                 update(models.users_sbv)
#                 .where(models.users_sbv.c.username == username)
#                 .values(**kwargs)
#                 .returning(*models.users_sbv.c)  # Trả về tất cả các cột
#             )
#         elif platform == 'ecommerce':
#             stmt = (
#                 update(models.users_ecommerce)
#                 .where(models.users_ecommerce.c.username == username)
#                 .values(**kwargs)
#                 .returning(*models.users_ecommerce.c)  # Trả về tất cả các cột
#             )
#         else:
#             raise ValidationError(f"Invalid platform: {platform}")

#         result = await session.execute(stmt)
#         await session.commit()
#         # Trả về dict chứa toàn bộ dữ liệu user vừa cập nhật
#         return result.mappings().first()
#     except IntegrityError as e:
#         raise DatabaseError(f"Database integrity error: {str(e)}")
#     except SQLAlchemyError as e:
#         raise DatabaseError(
#             f"Error updating {platform.upper()} user: {str(e)}")


# async def gen_app_log(session: AsyncSession, username: str, log_type: str, log_content: str, error_log: str):
#     try:
#         statement = text("""
#         INSERT INTO app_logs(username, log_type, log_content, created_at, error_log)
#         VALUES(:username, :log_type, :log_content, :created_at, :error_log)
#         """)

#         print(">>>>> GEN APP LOGS")

#         result = await session.execute(statement, {
#             "username": username,
#             "log_type": log_type,
#             "log_content": log_content,
#             "created_at": datetime.now(),
#             "error_log": error_log
#         })

#         await session.commit()
#         return result
#     except SQLAlchemyError as e:
#         raise DatabaseError(f"Error generating app log: {str(e)}")


# async def create_notification(session: AsyncSession, user_id: int, title: str, content: str, type: str):
#     try:
#         stmt = insert(models.notifications).values(
#             user_id=user_id,
#             title=title,
#             content=content,
#             type=type
#         )
#         await session.execute(stmt)
#         await session.commit()
#     except SQLAlchemyError as e:
#         raise DatabaseError(f"Error creating notification: {str(e)}")


# async def get_user_notifications(session: AsyncSession, user_id: int):
#     try:
#         stmt = select(models.notifications).where(models.notifications.c.user_id ==
#                                                   user_id).order_by(models.notifications.c.created_at.desc()).limit(100)
#         result = await session.execute(stmt)
#         return result.mappings().all()
#     except SQLAlchemyError as e:
#         raise DatabaseError(f"Error retrieving user notifications: {str(e)}")


# async def mark_as_read(session: AsyncSession, notification_id: int):
#     try:
#         stmt = update(models.notifications).where(models.notifications.c.id ==
#                                                   notification_id).values(is_read=True)
#         await session.execute(stmt)
#         await session.commit()
#     except SQLAlchemyError as e:
#         raise DatabaseError(f"Error marking notification as read: {str(e)}")


# async def create_request(session: AsyncSession, request: dict):
#     try:
#         stmt = insert(models.requests).values(request).returning(*models.requests.c)
#         request = await session.execute(stmt)
#         await session.commit()
#         return request.mappings().first()
#     except IntegrityError as e:
#         if "duplicate key" in str(e).lower():
#             raise DuplicateError(
#                 f"Request with title '{request.get('title')}' already exists")
#         raise DatabaseError(f"Database integrity error: {str(e)}")
#     except SQLAlchemyError as e:
#         raise DatabaseError(f"Error creating request: {str(e)}")


# async def get_request_by_id(session: AsyncSession, request_id: int):
#     try:
#         stmt = select(models.requests).where(models.requests.c.id == request_id)
#         result = await session.execute(stmt)
#         return result.mappings().first()
#     except SQLAlchemyError as e:
#         raise DatabaseError(f"Error retrieving request by id: {str(e)}")

# async def get_request_by_user_id(session: AsyncSession, user_id: int):
#     try:
#         if user_id == -1:
#             stmt = select(models.requests)
#         else:
#             stmt = select(models.requests).where(models.requests.c.requester_id == user_id)
#         result = await session.execute(stmt)
#         return result.mappings().all()
#     except SQLAlchemyError as e:
#         raise DatabaseError(f"Error retrieving requests: {str(e)}")


# async def update_request(session: AsyncSession, request_id: int, request: dict):
#     try:
#         stmt = update(models.requests).where(models.requests.c.id == request_id).values(request)
#         await session.execute(stmt)
#         await session.commit()
#     except SQLAlchemyError as e:
#         raise DatabaseError(f"Error updating request: {str(e)}")
