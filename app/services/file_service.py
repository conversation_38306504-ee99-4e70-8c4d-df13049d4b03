"""
File handling service utilities
"""

import uuid
from pathlib import Path
from typing import Optional
import mimetypes
from datetime import datetime

from fastapi import UploadFile
from core.errors import ValidationError, DatabaseError


class FileService:
    """Service for handling file operations"""

    def __init__(self, upload_dir: str = "uploads"):
        self.upload_dir = Path(upload_dir)
        self.upload_dir.mkdir(exist_ok=True)

        # Create subdirectories for organization
        self.user_files_dir = self.upload_dir / "user_files"
        self.user_files_dir.mkdir(exist_ok=True)

        # Maximum file size (50MB)
        self.max_file_size = 50 * 1024 * 1024

        # Allowed file types
        self.allowed_extensions = {
            '.txt', '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg',
            '.zip', '.rar', '.7z',
            '.csv', '.json', '.xml'
        }

    def validate_file(self, file: UploadFile) -> None:
        """Validate uploaded file"""
        if not file.filename:
            raise ValidationError("No file selected")

        # Check file size
        if file.size and file.size > self.max_file_size:
            raise ValidationError(f"File size exceeds maximum limit of {self.max_file_size // (1024*1024)}MB")

        # Check file extension
        file_ext = Path(file.filename).suffix.lower()
        if file_ext not in self.allowed_extensions:
            raise ValidationError(f"File type '{file_ext}' is not allowed")

    def generate_unique_filename(self, original_filename: str) -> str:
        """Generate a unique filename to prevent conflicts"""
        file_ext = Path(original_filename).suffix
        unique_id = str(uuid.uuid4())
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{timestamp}_{unique_id}{file_ext}"

    async def save_file(self, file: UploadFile, user_id: int, description: Optional[str] = None) -> dict:
        """Save uploaded file to disk and return file metadata"""
        try:
            # Validate file
            self.validate_file(file)

            # Generate unique filename
            unique_filename = self.generate_unique_filename(file.filename)

            # Create user-specific directory
            user_dir = self.user_files_dir / str(user_id)
            user_dir.mkdir(exist_ok=True)

            # Full file path
            file_path = user_dir / unique_filename

            # Save file to disk
            with open(file_path, "wb") as buffer:
                content = await file.read()
                buffer.write(content)

            # Get file info
            file_size = len(content)
            mime_type = mimetypes.guess_type(file.filename)[0]

            return {
                "filename": unique_filename,
                "original_filename": file.filename,
                "file_path": str(file_path),
                "file_size": file_size,
                "mime_type": mime_type,
                "description": description,
                "user_id": user_id
            }

        except Exception as e:
            raise DatabaseError(f"Error saving file: {str(e)}")

    def get_file_path(self, filename: str, user_id: int) -> Path:
        """Get the full path to a file"""
        return self.user_files_dir / str(user_id) / filename

    def delete_file(self, filename: str, user_id: int) -> bool:
        """Delete a file from disk"""
        try:
            file_path = self.get_file_path(filename, user_id)
            if file_path.exists():
                file_path.unlink()
                return True
            return False
        except Exception as e:
            raise DatabaseError(f"Error deleting file: {str(e)}")

    def file_exists(self, filename: str, user_id: int) -> bool:
        """Check if a file exists on disk"""
        file_path = self.get_file_path(filename, user_id)
        return file_path.exists()

    def get_file_size_mb(self, size_bytes: int) -> float:
        """Convert bytes to MB"""
        return round(size_bytes / (1024 * 1024), 2)


# Global file service instance
file_service = FileService()
