from sqlalchemy.future import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text, insert, update, delete, func
from datetime import datetime
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from sqlalchemy.orm import aliased
import json

from models import models
from core.errors import DatabaseError, NotFoundError, DuplicateError, ValidationError

async def create_notification(session: AsyncSession, user_id: int, title: str, content: str, type: str):
    try:
        stmt = insert(models.notifications).values(
            user_id=user_id,
            title=title,
            content=content,
            type=type
        )
        await session.execute(stmt)
        await session.commit()
    except SQLAlchemyError as e:
        raise DatabaseError(f"Error creating notification: {str(e)}")


async def get_user_notifications(session: AsyncSession, user_id: int):
    try:
        stmt = select(models.notifications).where(models.notifications.c.user_id ==
                                                  user_id).order_by(models.notifications.c.created_at.desc()).limit(100)
        result = await session.execute(stmt)
        return result.mappings().all()
    except SQLAlchemyError as e:
        raise DatabaseError(f"Error retrieving user notifications: {str(e)}")


async def mark_as_read(session: AsyncSession, notification_id: int):
    try:
        stmt = update(models.notifications).where(models.notifications.c.id ==
                                                  notification_id).values(is_read=True)
        await session.execute(stmt)
        await session.commit()
    except SQLAlchemyError as e:
        raise DatabaseError(f"Error marking notification as read: {str(e)}")