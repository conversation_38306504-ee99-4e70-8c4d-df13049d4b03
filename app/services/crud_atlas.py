"""
CRUD operations for Atlas Workbooks and Views
"""

from sqlalchemy import select, insert, update, delete
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql import func
from typing import List, Optional, Dict, Any, Union

from models.models import atlas_workbooks, atlas_views
from schemas.schemas_atlas import AtlasWorkbookCreate, AtlasWorkbookUpdate, AtlasViewCreate, AtlasViewUpdate
from core.errors import DatabaseError


# Atlas Workbooks CRUD operations

async def get_report_owners(session: AsyncSession) -> List[Dict[str, Any]]:
    """Get all report owners"""
    try:
        query = select(atlas_views.c.owner, atlas_views.c.owner_name).distinct()
        result = await session.execute(query)
        return [dict(row) for row in result.mappings().all()]
    except Exception as e:
        raise DatabaseError(f"Error retrieving report owners: {str(e)}")


async def get_all_workbooks(session: AsyncSession, page: int = 1, limit: int = 10, owner: Optional[str] = None, search: Optional[str] = None) -> List[Dict[str, Any]]:
    """Get all Atlas workbooks"""
    try:
        query = select(atlas_workbooks)
        if owner:
            query = query.where(atlas_workbooks.c.owner == owner)
        
        if search:
            query = query.where(atlas_workbooks.c.name.ilike(f"%{search}%"))
        
        query = query.order_by(atlas_workbooks.c.updated_at.desc()).limit(limit).offset((page - 1) * limit)
        
        result = await session.execute(query)
        return [dict(row) for row in result.mappings().all()]
    except Exception as e:
        raise DatabaseError(f"Error retrieving Atlas workbooks: {str(e)}")


async def get_workbook_by_id(session: AsyncSession, workbook_id: int) -> Optional[Dict[str, Any]]:
    """Get an Atlas workbook by ID"""
    try:
        query = select(atlas_workbooks).where(atlas_workbooks.c.id == workbook_id)
        result = await session.execute(query)
        row = result.mappings().first()
        return dict(row) if row else None
    except Exception as e:
        raise DatabaseError(f"Error retrieving Atlas workbook with ID {workbook_id}: {str(e)}")


async def create_workbook(session: AsyncSession, workbook: AtlasWorkbookCreate) -> Dict[str, Any]:
    """Create a new Atlas workbook"""
    try:
        workbook_data = workbook.dict()
        query = insert(atlas_workbooks).values(**workbook_data).returning(atlas_workbooks)
        result = await session.execute(query)
        await session.commit()
        return dict(result.mappings().first())
    except Exception as e:
        await session.rollback()
        raise DatabaseError(f"Error creating Atlas workbook: {str(e)}")


async def update_workbook(
    session: AsyncSession, workbook_id: int, workbook: AtlasWorkbookUpdate
) -> Optional[Dict[str, Any]]:
    """Update an Atlas workbook"""
    try:
        # Only update fields that are not None
        update_data = {k: v for k, v in workbook.dict().items() if v is not None}
        if not update_data:
            # If no fields to update, just return the current workbook
            return await get_workbook_by_id(session, workbook_id)
        
        query = (
            update(atlas_workbooks)
            .where(atlas_workbooks.c.id == workbook_id)
            .values(**update_data)
            .returning(atlas_workbooks)
        )
        result = await session.execute(query)
        await session.commit()
        row = result.mappings().first()
        return dict(row) if row else None
    except Exception as e:
        await session.rollback()
        raise DatabaseError(f"Error updating Atlas workbook with ID {workbook_id}: {str(e)}")


async def delete_workbook(session: AsyncSession, workbook_id: int) -> bool:
    """Delete an Atlas workbook"""
    try:
        query = delete(atlas_workbooks).where(atlas_workbooks.c.id == workbook_id)
        result = await session.execute(query)
        await session.commit()
        return result.rowcount > 0
    except Exception as e:
        await session.rollback()
        raise DatabaseError(f"Error deleting Atlas workbook with ID {workbook_id}: {str(e)}")


# Atlas Views CRUD operations

async def get_all_views(session: AsyncSession, page: int = 1, limit: int = 10, owner: Optional[str] = None, search: Optional[str] = None) -> List[Dict[str, Any]]:
    """Get all Atlas views"""
    try:
        query = select(atlas_views)
        if owner:
            query = query.where(atlas_views.c.owner == owner)
        
        if search:
            query = query.where(atlas_views.c.name.ilike(f"%{search}%"))
        
        query = query.order_by(atlas_views.c.updated_at.desc()).limit(limit).offset((page - 1) * limit)
        
        result = await session.execute(query)
        return [dict(row) for row in result.mappings().all()]
    except Exception as e:
        raise DatabaseError(f"Error retrieving Atlas views: {str(e)}")


async def get_view_by_id(session: AsyncSession, view_id: int) -> Optional[Dict[str, Any]]:
    """Get an Atlas view by ID"""
    try:
        query = select(atlas_views).where(atlas_views.c.id == view_id)
        result = await session.execute(query)
        row = result.mappings().first()
        return dict(row) if row else None
    except Exception as e:
        raise DatabaseError(f"Error retrieving Atlas view with ID {view_id}: {str(e)}")


async def get_views_by_workbook_id(session: AsyncSession, workbook_id: int) -> List[Dict[str, Any]]:
    """Get all views for a specific workbook"""
    try:
        query = select(atlas_views).where(atlas_views.c.workbook_id == workbook_id)
        result = await session.execute(query)
        return [dict(row) for row in result.mappings().all()]
    except Exception as e:
        raise DatabaseError(f"Error retrieving views for workbook ID {workbook_id}: {str(e)}")


async def create_view(session: AsyncSession, view: AtlasViewCreate) -> Dict[str, Any]:
    """Create a new Atlas view"""
    try:
        view_data = view.dict()
        query = insert(atlas_views).values(**view_data).returning(atlas_views)
        result = await session.execute(query)
        await session.commit()
        return dict(result.mappings().first())
    except Exception as e:
        await session.rollback()
        raise DatabaseError(f"Error creating Atlas view: {str(e)}")


async def update_view(
    session: AsyncSession, view_id: int, view: AtlasViewUpdate
) -> Optional[Dict[str, Any]]:
    """Update an Atlas view"""
    try:
        # Only update fields that are not None
        update_data = {k: v for k, v in view.dict().items() if v is not None}
        if not update_data:
            # If no fields to update, just return the current view
            return await get_view_by_id(session, view_id)
        
        query = (
            update(atlas_views)
            .where(atlas_views.c.id == view_id)
            .values(**update_data)
            .returning(atlas_views)
        )
        result = await session.execute(query)
        await session.commit()
        row = result.mappings().first()
        return dict(row) if row else None
    except Exception as e:
        await session.rollback()
        raise DatabaseError(f"Error updating Atlas view with ID {view_id}: {str(e)}")


async def delete_view(session: AsyncSession, view_id: int) -> bool:
    """Delete an Atlas view"""
    try:
        query = delete(atlas_views).where(atlas_views.c.id == view_id)
        result = await session.execute(query)
        await session.commit()
        return result.rowcount > 0
    except Exception as e:
        await session.rollback()
        raise DatabaseError(f"Error deleting Atlas view with ID {view_id}: {str(e)}")


async def get_workbook_with_views(session: AsyncSession, workbook_id: int) -> Optional[Dict[str, Any]]:
    """Get a workbook with all its views"""
    try:
        workbook = await get_workbook_by_id(session, workbook_id)
        if not workbook:
            return None
        
        views = await get_views_by_workbook_id(session, workbook_id)
        workbook["views"] = views
        return workbook
    except Exception as e:
        raise DatabaseError(f"Error retrieving workbook with views for ID {workbook_id}: {str(e)}")
