from sqlalchemy.future import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text, insert, update, delete, func
from datetime import datetime
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from sqlalchemy.orm import aliased
import json

from models import models
from core.errors import DatabaseError, NotFoundError, DuplicateError, ValidationError

# File management CRUD functions
async def create_file_record(session: AsyncSession, file_data: dict):
    """
    Create a new file record in database
    """
    try:
        stmt = (
            insert(models.files)
            .values(file_data)
            .returning(*models.files.c)
        )
        result = await session.execute(stmt)
        file_record = result.mappings().first()

        await session.commit()
        return file_record
    except IntegrityError as e:
        raise DatabaseError(f"Database integrity error: {str(e)}")
    except SQLAlchemyError as e:
        raise DatabaseError(f"Error creating file record: {str(e)}")


async def get_file_by_id(session: AsyncSession, file_id: int):
    """
    Get file record by ID with user information
    """
    try:
        stmt = (
            select(
                models.files,
                models.users.c.username.label("username"),
                models.users.c.display_name.label("user_display_name")
            )
            .select_from(models.files)
            .join(models.users, models.files.c.user_id == models.users.c.id, isouter=True)
            .where(models.files.c.id == file_id)
            .where(models.files.c.status == "active")
        )
        result = await session.execute(stmt)
        return result.mappings().first()
    except SQLAlchemyError as e:
        raise DatabaseError(f"Error retrieving file: {str(e)}")


async def get_files_by_user_id(session: AsyncSession, user_id: int, page: int = 1, limit: int = 10):
    """
    Get all files for a specific user with pagination
    """
    try:
        offset = (page - 1) * limit

        # Get files
        stmt = (
            select(models.files)
            .where(models.files.c.user_id == user_id)
            .where(models.files.c.status == "active")
            .order_by(models.files.c.created_at.desc())
            .offset(offset)
            .limit(limit)
        )
        files_result = await session.execute(stmt)
        files = files_result.mappings().all()

        # Get total count
        count_stmt = (
            select(func.count())
            .select_from(models.files)
            .where(models.files.c.user_id == user_id)
            .where(models.files.c.status == "active")
        )
        total_count = (await session.execute(count_stmt)).scalar()

        return {
            "files": files,
            "total_count": total_count,
            "page": page,
            "limit": limit
        }
    except SQLAlchemyError as e:
        raise DatabaseError(f"Error retrieving user files: {str(e)}")


async def delete_file_record(session: AsyncSession, file_id: int, user_id: int):
    """
    Soft delete a file record (mark as deleted)
    """
    try:
        # Check if file belongs to user
        file_record = await get_file_by_id(session, file_id)
        if not file_record:
            raise NotFoundError(f"File with ID {file_id} not found")

        if file_record.get("user_id") != user_id:
            raise ValidationError("You can only delete your own files")

        # Soft delete
        stmt = (
            update(models.files)
            .where(models.files.c.id == file_id)
            .where(models.files.c.user_id == user_id)
            .values(status="deleted", updated_at=datetime.now())
            .returning(*models.files.c)
        )
        result = await session.execute(stmt)
        await session.commit()
        return result.mappings().first()
    except SQLAlchemyError as e:
        raise DatabaseError(f"Error deleting file record: {str(e)}")


async def get_all_files(session: AsyncSession, page: int = 1, limit: int = 10):
    """
    Get all files (admin only) with pagination
    """
    try:
        offset = (page - 1) * limit

        # Get files with user info
        stmt = (
            select(
                models.files,
                models.users.c.username.label("username"),
                models.users.c.display_name.label("user_display_name")
            )
            .select_from(models.files)
            .join(models.users, models.files.c.user_id == models.users.c.id, isouter=True)
            .where(models.files.c.status == "active")
            .order_by(models.files.c.created_at.desc())
            .offset(offset)
            .limit(limit)
        )
        files_result = await session.execute(stmt)
        files = files_result.mappings().all()

        # Get total count
        count_stmt = (
            select(func.count())
            .select_from(models.files)
            .where(models.files.c.status == "active")
        )
        total_count = (await session.execute(count_stmt)).scalar()

        return {
            "files": files,
            "total_count": total_count,
            "page": page,
            "limit": limit
        }
    except SQLAlchemyError as e:
        raise DatabaseError(f"Error retrieving all files: {str(e)}")