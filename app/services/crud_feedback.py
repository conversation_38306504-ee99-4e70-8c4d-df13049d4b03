from sqlalchemy.future import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text, insert, update, delete, func
from datetime import datetime
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from sqlalchemy.orm import aliased
import json

from models import models
from core.errors import DatabaseError, NotFoundError, DuplicateError, ValidationError

async def create_feedback(session: AsyncSession, feedback_data: dict):
    """
    Create a new feedback entry
    """
    try:
        stmt = (
            insert(models.feedback)
            .values(feedback_data)
            .returning(*models.feedback.c)
        )
        result = await session.execute(stmt)
        feedback = result.mappings().first()

        await session.commit()
        return feedback
    except IntegrityError as e:
        raise DatabaseError(f"Database integrity error: {str(e)}")
    except SQLAlchemyError as e:
        raise DatabaseError(f"Error creating feedback: {str(e)}")


async def get_feedback_by_id(session: AsyncSession, feedback_id: int):
    """
    Get feedback by ID with user information
    """
    try:
        stmt = (
            select(
                models.feedback,
                models.users.c.username.label("username"),
                models.users.c.display_name.label("user_display_name")
            )
            .select_from(models.feedback)
            .join(models.users, models.feedback.c.user_id == models.users.c.id, isouter=True)
            .where(models.feedback.c.id == feedback_id)
        )
        result = await session.execute(stmt)
        return result.mappings().first()
    except SQLAlchemyError as e:
        raise DatabaseError(f"Error retrieving feedback: {str(e)}")


async def get_feedback_by_user_id(session: AsyncSession, user_id: int):
    """
    Get all feedback entries for a specific user
    """
    try:
        stmt = (
            select(models.feedback)
            .where(models.feedback.c.user_id == user_id)
            .order_by(models.feedback.c.created_at.desc())
        )
        result = await session.execute(stmt)
        return result.mappings().all()
    except SQLAlchemyError as e:
        raise DatabaseError(f"Error retrieving user feedback: {str(e)}")


async def update_feedback_status(session: AsyncSession, feedback_id: int, status: str):
    """
    Update feedback status
    """
    try:
        stmt = (
            update(models.feedback)
            .where(models.feedback.c.id == feedback_id)
            .values(status=status, updated_at=datetime.now())
            .returning(*models.feedback.c)
        )
        result = await session.execute(stmt)
        await session.commit()
        return result.mappings().first()
    except SQLAlchemyError as e:
        raise DatabaseError(f"Error updating feedback status: {str(e)}")

