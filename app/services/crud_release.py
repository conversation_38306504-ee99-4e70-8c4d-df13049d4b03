from sqlalchemy.future import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import insert, update, delete
from sqlalchemy.exc import SQLAlchemyError, IntegrityError

from models import models
from core.errors import DatabaseError, NotFoundError, DuplicateError, ValidationError


async def get_all_release_notes(session: AsyncSession):
    """Get all release notes"""
    try:
        result = (await session.execute(select(models.release_notes))).all()
        return result
    except SQLAlchemyError as e:
        raise DatabaseError(f"Error retrieving release notes: {str(e)}")


async def get_release_note_by_id(session: AsyncSession, note_id: int):
    """Get a release note by ID"""
    try:
        result = (await session.execute(
            select(models.release_notes).where(models.release_notes.c.id == note_id)
        )).first()
        
        if not result:
            raise NotFoundError(f"Release note with ID {note_id} not found")
            
        return result
    except NotFoundError:
        raise
    except SQLAlchemyError as e:
        raise DatabaseError(f"Error retrieving release note: {str(e)}")


async def get_release_note_by_version(session: AsyncSession, version: str):
    """Get a release note by version"""
    try:
        result = (await session.execute(
            select(models.release_notes).where(models.release_notes.c.version == version)
        )).first()
        
        return result
    except SQLAlchemyError as e:
        raise DatabaseError(f"Error retrieving release note: {str(e)}")


async def create_release_note(session: AsyncSession, release_note: dict):
    """Create a new release note"""
    try:
        stmt = (
            insert(models.release_notes)
            .values(**release_note)
            .returning(*models.release_notes.c)
        )
        result = await session.execute(stmt)
        await session.commit()
        return result.mappings().first()
    except IntegrityError as e:
        if "duplicate key" in str(e).lower():
            raise DuplicateError(f"Release note with version '{release_note.get('version')}' already exists")
        raise DatabaseError(f"Database integrity error: {str(e)}")
    except SQLAlchemyError as e:
        raise DatabaseError(f"Error creating release note: {str(e)}")


async def update_release_note(session: AsyncSession, note_id: int, release_note: dict):
    """Update an existing release note"""
    try:
        # Check if release note exists
        await get_release_note_by_id(session, note_id)
        
        stmt = (
            update(models.release_notes)
            .where(models.release_notes.c.id == note_id)
            .values(**release_note)
            .returning(*models.release_notes.c)
        )
        result = await session.execute(stmt)
        await session.commit()
        return result.mappings().first()
    except NotFoundError:
        raise
    except SQLAlchemyError as e:
        raise DatabaseError(f"Error updating release note: {str(e)}")


async def delete_release_note(session: AsyncSession, note_id: int):
    """Delete a release note"""
    try:
        # Check if release note exists
        await get_release_note_by_id(session, note_id)
        
        stmt = (
            delete(models.release_notes)
            .where(models.release_notes.c.id == note_id)
            .returning(*models.release_notes.c)
        )
        result = await session.execute(stmt)
        await session.commit()
        return result.mappings().first()
    except NotFoundError:
        raise
    except SQLAlchemyError as e:
        raise DatabaseError(f"Error deleting release note: {str(e)}")
