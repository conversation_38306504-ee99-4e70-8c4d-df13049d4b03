import logging
import os
from datetime import datetime
from typing import Optional
import pathlib

# Get the absolute path to the application root directory
APP_ROOT = pathlib.Path(__file__).parent.parent.parent.absolute()
LOGS_DIR = os.path.join(APP_ROOT, "logs")

# Create logs directory if it doesn't exist
os.makedirs(LOGS_DIR, exist_ok=True)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(LOGS_DIR, f"app_{datetime.now().strftime('%Y%m%d')}.log"))
    ]
)

# Create logger
logger = logging.getLogger("app")


def log_error(
    error: Exception, 
    context: Optional[str] = None, 
    user: Optional[str] = None,
    additional_info: Optional[dict] = None
):
    """
    Log an error with context information
    
    Args:
        error: The exception that was raised
        context: The context where the error occurred (e.g., function name)
        user: The user who triggered the error
        additional_info: Any additional information to log
    """
    error_message = f"Error: {str(error)}"
    
    if context:
        error_message = f"[{context}] {error_message}"
    
    if user:
        error_message = f"User: {user} - {error_message}"
    
    if additional_info:
        error_message = f"{error_message} - Additional Info: {additional_info}"
    
    logger.error(error_message, exc_info=True)


def log_info(message: str, context: Optional[str] = None, user: Optional[str] = None):
    """
    Log an info message
    
    Args:
        message: The message to log
        context: The context where the message is from (e.g., function name)
        user: The user who triggered the message
    """
    info_message = message
    
    if context:
        info_message = f"[{context}] {info_message}"
    
    if user:
        info_message = f"User: {user} - {info_message}"
    
    logger.info(info_message)


def log_warning(message: str, context: Optional[str] = None, user: Optional[str] = None):
    """
    Log a warning message
    
    Args:
        message: The message to log
        context: The context where the message is from (e.g., function name)
        user: The user who triggered the message
    """
    warning_message = message
    
    if context:
        warning_message = f"[{context}] {warning_message}"
    
    if user:
        warning_message = f"User: {user} - {warning_message}"
    
    logger.warning(warning_message) 