from fastapi import Request, status
from fastapi.responses import JSONResponse
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from pydantic import ValidationError as PydanticValidationError
from typing import Union, Dict, Any

from .errors import AppException, ErrorResponse
from .logger import log_error


async def exception_handler_middleware(request: Request, call_next):
    """Global exception handler middleware"""
    try:
        return await call_next(request)
    except Exception as exc:
        return handle_exception(exc, request)


def handle_exception(exc: Exception, request: Request = None) -> JSONResponse:
    """Handle different types of exceptions and return appropriate responses"""
    
    # Get user info if available
    user = None
    if request and hasattr(request, "state") and hasattr(request.state, "user"):
        user = request.state.user
    
    # Handle custom application exceptions
    if isinstance(exc, AppException):
        log_error(exc, context="AppException", user=user)
        return JSONResponse(
            status_code=exc.status_code,
            content=ErrorResponse(
                error=exc.error,
                detail=exc.detail
            ).dict()
        )
    
    # Handle Pydantic validation errors
    if isinstance(exc, PydanticValidationError):
        log_error(exc, context="ValidationError", user=user)
        return JSONResponse(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            content=ErrorResponse(
                error="Validation Error",
                detail=str(exc)
            ).dict()
        )
    
    # Handle SQLAlchemy errors
    if isinstance(exc, IntegrityError):
        # Check for unique constraint violations
        if "duplicate key" in str(exc).lower():
            log_error(exc, context="IntegrityError", user=user, additional_info={"type": "duplicate_key"})
            return JSONResponse(
                status_code=status.HTTP_409_CONFLICT,
                content=ErrorResponse(
                    error="Duplicate Entry",
                    detail="A record with this information already exists"
                ).dict()
            )
        log_error(exc, context="IntegrityError", user=user)
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content=ErrorResponse(
                error="Database Integrity Error",
                detail=str(exc)
            ).dict()
        )
    
    if isinstance(exc, SQLAlchemyError):
        log_error(exc, context="SQLAlchemyError", user=user)
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=ErrorResponse(
                error="Database Error",
                detail="An error occurred while accessing the database"
            ).dict()
        )
    
    # Handle all other exceptions
    log_error(exc, context="UnknownError", user=user)
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=ErrorResponse(
            error="Internal Server Error",
            detail=str(exc)
        ).dict()
    ) 