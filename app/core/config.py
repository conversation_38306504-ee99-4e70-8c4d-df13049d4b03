from pydantic import BaseSettings
from dotenv import load_dotenv
import os

class Settings(BaseSettings):
    app_name: str = "DataQuest System"
    # Thêm các biến môi trường khác mà bạn cần ở đây
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

def get_config():
    # Load biến môi trường từ file .env
    load_dotenv()
    # Trả về instance của Settings
    return Settings()

# Khởi tạo settings instance
settings = get_config()
