from fastapi import HTTPException, status
from typing import Any, Dict, Optional
from pydantic import BaseModel


class ErrorResponse(BaseModel):
    """Standard error response model"""
    error: str
    detail: Optional[str] = None
    code: Optional[int] = None


class AppException(HTTPException):
    """Base exception for application errors"""
    def __init__(
        self, 
        status_code: int, 
        error: str, 
        detail: Optional[str] = None,
        headers: Optional[Dict[str, Any]] = None
    ):
        super().__init__(status_code=status_code, detail=detail, headers=headers)
        self.error = error


class ValidationError(AppException):
    """Exception for validation errors"""
    def __init__(self, detail: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            error="Validation Error",
            detail=detail
        )


class AuthenticationError(AppException):
    """Exception for authentication errors"""
    def __init__(self, detail: str = "Authentication failed"):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            error="Authentication Error",
            detail=detail
        )


class AuthorizationError(AppException):
    """Exception for authorization errors"""
    def __init__(self, detail: str = "Permission denied"):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            error="Authorization Error",
            detail=detail
        )


class NotFoundError(AppException):
    """Exception for not found errors"""
    def __init__(self, detail: str):
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            error="Not Found",
            detail=detail
        )


class DatabaseError(AppException):
    """Exception for database errors"""
    def __init__(self, detail: str):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error="Database Error",
            detail=detail
        )


class DuplicateError(AppException):
    """Exception for duplicate entry errors"""
    def __init__(self, detail: str):
        super().__init__(
            status_code=status.HTTP_409_CONFLICT,
            error="Duplicate Entry",
            detail=detail
        )


class ExternalServiceError(AppException):
    """Exception for external service errors"""
    def __init__(self, detail: str):
        super().__init__(
            status_code=status.HTTP_502_BAD_GATEWAY,
            error="External Service Error",
            detail=detail
        ) 