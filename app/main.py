import uvicorn
from fastapi import FastAP<PERSON>, Depends, Request
from fastapi.responses import JSONResponse

from api.auth.routers import itemrouter
from api.users.routers import itemrouter as user_router
from api.users.routers_wlp import itemrouter as user_router_wlp
from api.users.routers_ads import itemrouter as user_router_ads
from api.users.routers_sbv import itemrouter as user_router_sbv
from api.users.routers_ecommerce import itemrouter as user_router_ecommerce
from api.config.routers import itemrouter as config_router

from api.notifications.routers import itemrouter as notification_router
from api.requests.routers import itemrouter as request_router
from api.release.routers import itemrouter as release_router
from api.atlas.routers import itemrouter as atlas_router
from api.feedback.routers import itemrouter as feedback_router
from api.files.routers import itemrouter as files_router

from starlette.middleware.sessions import SessionMiddleware
from starlette.middleware.cors import CORSMiddleware
from core import config
from db.database import init_db
from core.middleware import exception_handler_middleware
from core.errors import AppException, ErrorResponse


tags_metadata = [
    # {"name": "ads_dev", "description": "Ads-Report Tool for Dev", },
    # {"name": "ads", "description": "Ads-Report Tool", },
]

app = FastAPI(title="DataQuest BE", openapi_tags=tags_metadata, dependencies=[Depends(config.get_config)])

# Add exception handler middleware
app.middleware("http")(exception_handler_middleware)

# Add exception handlers for specific exceptions
@app.exception_handler(AppException)
async def app_exception_handler(request: Request, exc: AppException):
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            error=exc.error,
            detail=exc.detail
        ).dict()
    )

app.include_router(itemrouter)
app.include_router(user_router)
app.include_router(user_router_wlp)
app.include_router(user_router_ads)
app.include_router(user_router_sbv)
app.include_router(user_router_ecommerce)
app.include_router(notification_router)
app.include_router(request_router)
app.include_router(config_router)
app.include_router(release_router)
app.include_router(atlas_router)
app.include_router(feedback_router)
app.include_router(files_router)

# app.add_middleware(Hoa
#     SessionMiddleware,
#     secret_key="!secret",
#     max_age=86400,
#     https_only=False,
#     same_site="",
# )  # 1 day

origins = [
    # "http://localhost:3012",
    # "http://localhost:3002",
    # "http://localhost:3000",
    # "https://localhost:3002",
    # # "https://localhost:3018",
    # "https://kpi.alphacloud.vn",
    # "http://kpi.alphacloud.vn",
    # "http://***********:8082"
    "*"
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.on_event("startup")
async def on_startup():
    await init_db()


# @app.on_event("shutdown")
# async def shutdown():
#     await disconnect()

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=3014)
