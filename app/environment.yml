name: env217
channels:
  - defaults
dependencies:
  - appdirs=1.4.4
  - attrs=21.4.0
  - autopep8=1.6.0
  - black=19.10b0
  - ca-certificates=2022.3.29
  - certifi=2021.10.8
  - libcxx=12.0.0
  - libffi=3.3
  - mypy_extensions=0.4.3
  - ncurses=6.2
  - openssl=1.1.1n
  - pathspec=0.7.0
  - pip=21.2.2
  - pycodestyle=2.8.0
  - python=3.8.5
  - readline=8.1
  - regex=2021.11.2
  - setuptools=58.0.4
  - sqlite=3.36.0
  - tk=8.6.10
  - toml=0.10.2
  - typed-ast=1.4.3
  - wheel=0.37.0
  - xz=5.2.5
  - zlib=1.2.11
  - pip:
    - aiofiles==0.8.0
    - aiohttp==3.8.1
    - aiopg==1.3.4
    - aiosignal==1.2.0
    - anyio==3.7.1
    - asgiref==3.4.1
    - async-timeout==4.0.2
    - asyncpg==0.26.0
    - backports-entry-points-selectable==1.1.0
    - bcrypt==3.2.2
    - cffi==1.15.0
    - charset-normalizer==2.0.6
    - click==8.0.1
    - conda-pack==0.6.0
    - cryptography==38.0.3
    - databases==0.6.0
    - distlib==0.3.2
    - ecdsa==0.17.0
    - elastic-transport==8.1.2
    - elasticsearch==7.13.4
    - elasticsearch-async==6.2.0
    - elasticsearch-dbapi==0.2.9
    - elasticsearch-dsl==7.4.0
    - exceptiongroup==1.2.0
    - fastapi==0.104.1
    - filelock==3.0.12
    - frozenlist==1.3.0
    - greenlet==1.1.1
    - h11==0.14.0
    - h2==4.1.0
    - hpack==4.0.0
    - httpcore==1.0.2
    - hypercorn==0.15.0
    - hyperframe==6.0.1
    - idna==3.2
    - install==1.3.5
    - itsdangerous==2.0.1
    - jinja2==3.1.1
    - ldap3==2.9.1
    - lxml==4.6.3
    - markupsafe==2.1.1
    - multidict==6.0.2
    - numpy==1.21.2
    - packaging==21.3
    - pandas==1.3.3
    - passlib==1.7.4
    - platformdirs==2.3.0
    - priority==2.0.0
    - psycopg2-binary==2.9.1
    - pyasn1==0.4.8
    - pycparser==2.21
    - pydantic==1.8.2
    - pyjwt==2.6.0
    - pyparsing==3.0.9
    - python-cas==1.6.0
    - python-dateutil==2.8.2
    - python-dotenv==0.20.0
    - python-jose==3.3.0
    - python-multipart==0.0.5
    - pytz==2021.1
    - pyyaml==6.0.1
    - requests==2.26.0
    - rsa==4.7.2
    - six==1.16.0
    - sniffio==1.3.0
    - sqlalchemy==1.4.39
    - sqlalchemy2-stubs==0.0.2a24
    - sqlmodel==0.0.6
    - starlette==0.27.0
    - taskgroup==0.0.0a4
    - tomli==2.0.1
    - typing-extensions==4.8.0
    - urllib3==1.26.6
    - uvicorn==0.24.0.post1
    - virtualenv==20.8.0
    - wsproto==1.2.0
    - yarl==1.7.2
prefix: /Users/<USER>/opt/anaconda3/envs/env217
