import os

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlmodel import SQLModel

import core.config as config
config.get_config()

host = os.environ.get("PG_HOST")
port = os.environ.get("PG_PORT")
user = os.environ.get("PG_USER")
password = os.environ.get("PG_PASSWORD")
db = os.environ.get("PG_DB")
schema_name = os.environ.get("PG_SCHEMA")
dbtype = os.environ.get("PG_DBTYPE")

DATABASE_URL = f"{dbtype}://{user}:{password}@{host}:{port}/{db}"

# print(">>>>>>", DATABASE_URL)

engine = create_async_engine(DATABASE_URL, pool_size=100, max_overflow=0, echo=False, future=True)

Base = declarative_base()


async def init_db():
    async with engine.begin() as conn:
        # await conn.run_sync(SQLModel.metadata.drop_all) # DROP ALL TABLE IN SESSION!!!!!!
        await conn.run_sync(SQLModel.metadata.create_all)


async def get_session() -> AsyncSession:
    async_session = sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False,
    )
    async with async_session() as session:
        yield session
