# This file may be used to create an environment using:
# $ conda create --name <env> --file <this file>
# platform: osx-64
aiofiles=0.8.0=pypi_0
aiohttp=3.8.1=pypi_0
aiopg=1.3.4=pypi_0
aiosignal=1.2.0=pypi_0
anyio=3.7.1=pypi_0
appdirs=1.4.4=pyhd3eb1b0_0
asgiref=3.4.1=pypi_0
async-timeout=4.0.2=pypi_0
asyncpg=0.26.0=pypi_0
attrs=21.4.0=pyhd3eb1b0_0
autopep8=1.6.0=pyhd3eb1b0_0
backports-entry-points-selectable=1.1.0=pypi_0
bcrypt=3.2.2=pypi_0
black=19.10b0=py_0
ca-certificates=2022.3.29=hecd8cb5_0
certifi=2021.10.8=py38hecd8cb5_2
cffi=1.15.0=pypi_0
charset-normalizer=2.0.6=pypi_0
click=8.0.1=pypi_0
conda-pack=0.6.0=pypi_0
cryptography=38.0.3=pypi_0
databases=0.6.0=pypi_0
distlib=0.3.2=pypi_0
ecdsa=0.17.0=pypi_0
elastic-transport=8.1.2=pypi_0
elasticsearch=7.13.4=pypi_0
elasticsearch-async=6.2.0=pypi_0
elasticsearch-dbapi=0.2.9=pypi_0
elasticsearch-dsl=7.4.0=pypi_0
exceptiongroup=1.2.0=pypi_0
fastapi=0.104.1=pypi_0
filelock=3.0.12=pypi_0
frozenlist=1.3.0=pypi_0
greenlet=1.1.1=pypi_0
h11=0.14.0=pypi_0
h2=4.1.0=pypi_0
hpack=4.0.0=pypi_0
httpcore=1.0.2=pypi_0
hypercorn=0.15.0=pypi_0
hyperframe=6.0.1=pypi_0
idna=3.2=pypi_0
install=1.3.5=pypi_0
itsdangerous=2.0.1=pypi_0
jinja2=3.1.1=pypi_0
ldap3=2.9.1=pypi_0
libcxx=12.0.0=h2f01273_0
libffi=3.3=hb1e8313_2
lxml=4.6.3=pypi_0
markupsafe=2.1.1=pypi_0
multidict=6.0.2=pypi_0
mypy_extensions=0.4.3=py38hecd8cb5_1
ncurses=6.2=h0a44026_1
numpy=1.21.2=pypi_0
openssl=1.1.1n=hca72f7f_0
packaging=21.3=pypi_0
pandas=1.3.3=pypi_0
passlib=1.7.4=pypi_0
pathspec=0.7.0=py_0
pip=21.2.2=py38hecd8cb5_0
platformdirs=2.3.0=pypi_0
priority=2.0.0=pypi_0
psycopg2-binary=2.9.1=pypi_0
pyasn1=0.4.8=pypi_0
pycodestyle=2.8.0=pyhd3eb1b0_0
pycparser=2.21=pypi_0
pydantic=1.8.2=pypi_0
pyjwt=2.6.0=pypi_0
pyparsing=3.0.9=pypi_0
python=3.8.5=h26836e1_1
python-cas=1.6.0=pypi_0
python-dateutil=2.8.2=pypi_0
python-dotenv=0.20.0=pypi_0
python-jose=3.3.0=pypi_0
python-multipart=0.0.5=pypi_0
pytz=2021.1=pypi_0
pyyaml=6.0.1=pypi_0
readline=8.1=h9ed2024_0
regex=2021.11.2=py38hca72f7f_0
requests=2.26.0=pypi_0
rsa=4.7.2=pypi_0
setuptools=58.0.4=py38hecd8cb5_0
six=1.16.0=pypi_0
sniffio=1.3.0=pypi_0
sqlalchemy=1.4.39=pypi_0
sqlalchemy2-stubs=0.0.2a24=pypi_0
sqlite=3.36.0=hce871da_0
sqlmodel=0.0.6=pypi_0
starlette=0.27.0=pypi_0
taskgroup=0.0.0a4=pypi_0
tk=8.6.10=hb0a8c7a_0
toml=0.10.2=pyhd3eb1b0_0
tomli=2.0.1=pypi_0
typed-ast=1.4.3=py38h9ed2024_1
typing-extensions=********=pypi_0
urllib3=1.26.6=pypi_0
uvicorn=0.24.0.post1=pypi_0
virtualenv=20.8.0=pypi_0
wheel=0.37.0=pyhd3eb1b0_1
wsproto=1.2.0=pypi_0
xz=5.2.5=h1de35cc_0
yarl=1.7.2=pypi_0
zlib=1.2.11=h1de35cc_3
