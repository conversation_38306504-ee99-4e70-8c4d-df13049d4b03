"""
Pydantic models for release notes
"""

from typing import Optional
from pydantic import BaseModel
from datetime import date


class ReleaseNoteBase(BaseModel):
    version: str
    status: str
    complete_percent: int
    release_date: Optional[date] = None
    content: Optional[str] = None


class ReleaseNoteCreate(ReleaseNoteBase):
    pass


class ReleaseNoteUpdate(BaseModel):
    version: Optional[str] = None
    status: Optional[str] = None
    complete_percent: Optional[int] = None
    release_date: Optional[date] = None
    content: Optional[str] = None


class ReleaseNoteResponse(ReleaseNoteBase):
    id: int

    class Config:
        orm_mode = True
