"""
To avoid confusion between the SQLAlchemy models and the Pydantic models, we will have the file models.py with the SQLAlchemy models, and the file ORM.py with the Pydantic models.
"""

# from __future__ import annotations
from typing import Any, List, Optional, Union
from pydantic import BaseModel, Field

class UserBasePlatformRequest(BaseModel):
    username: str
    is_admin: bool = False
    status: str = 'active'
    # role: str
    # hashed_password: str
    
class UserSBVRequest(UserBasePlatformRequest):
    role: str

class UserEcommerceRequest(UserBasePlatformRequest):
    role: str

class UserWLPRequest(UserBasePlatformRequest):
    role: Optional[List[str]] = Field(default_factory=list)
    role_lvl1: Optional[List[str]] = Field(default_factory=list)
    # role_lvl2: Optional[List[str]] = Field(default_factory=list)
    # role_lvl3: Optional[List[str]] = Field(default_factory=list)
    department: Optional[str] = None

class UserADSRequest(UserBasePlatformRequest):
    role: Optional[List[str]] = Field(default_factory=list)
    hashed_password: Optional[str] = None
    user_group: Optional[str] = None
    user_type: Optional[str] = None
