"""
Pydantic models for Dashboard API responses
"""

from typing import Optional, List, Any, Dict
from pydantic import BaseModel
from datetime import datetime


class DashboardSummary(BaseModel):
    """Dashboard summary statistics"""
    total_pending_requests: int
    total_reviewed_approved_requests: int
    total_in_progress_requests: int
    total_platform_access: int
    total_adhoc_data: int
    total_atlas_reports: int


class RecentActivityItem(BaseModel):
    """Recent activity item"""
    id: int
    title: str
    description: Optional[str] = None
    request_type: str
    status: str
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class PlatformAccess(BaseModel):
    """Platform access information"""
    platform: str
    status: str
    role: List[str] = []
    is_admin: bool = False


class AccessItem(BaseModel):
    """Access item for atlas reports or adhoc data"""
    id: int
    title: str
    status: str
    created_at: datetime
    updated_at: Optional[datetime] = None


class MyAccess(BaseModel):
    """User's access information"""
    platforms: List[PlatformAccess] = []
    atlas_reports: List[AccessItem] = []
    adhoc_data: List[AccessItem] = []


class DashboardResponse(BaseModel):
    """Complete dashboard response"""
    summary: DashboardSummary
    recent_activity: List[RecentActivityItem] = []
    my_access: MyAccess
