"""
Pydantic models for Atlas Workbooks and Views
"""

from typing import Optional, List
from pydantic import BaseModel, Field
from datetime import datetime


class AtlasWorkbookBase(BaseModel):
    """
    Base model for Atlas Workbooks
    
    Field mappings:
    - name -> title
    - description -> description
    - thumbnail_id -> defaultViewThumbnailId
    - owner -> ownerAlias
    - owner_name -> ownerName
    - owner_email -> ownerEmail
    """
    name: str
    description: Optional[str] = None
    thumbnail_id: Optional[str] = None
    owner: Optional[str] = None
    owner_name: Optional[str] = None
    owner_email: Optional[str] = None


class AtlasWorkbookCreate(AtlasWorkbookBase):
    """Model for creating a new Atlas Workbook"""
    pass


class AtlasWorkbookUpdate(BaseModel):
    """Model for updating an Atlas Workbook"""
    name: Optional[str] = None
    description: Optional[str] = None
    thumbnail_id: Optional[str] = None
    owner: Optional[str] = None
    owner_name: Optional[str] = None
    owner_email: Optional[str] = None


class AtlasWorkbookInDB(AtlasWorkbookBase):
    """Model for Atlas Workbook as stored in the database"""
    id: int
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class AtlasViewBase(BaseModel):
    """
    Base model for Atlas Views
    
    Field mappings:
    - name -> title
    - path -> path
    - thumbnail_id -> thumbnailId
    - workbook_id -> workbook_id (foreign key to atlas_workbooks)
    - owner -> ownerAlias
    - owner_name -> ownerName
    - owner_email -> ownerEmail
    """
    name: str
    path: Optional[str] = None
    thumbnail_id: Optional[str] = None
    workbook_id: Optional[int] = None
    owner: Optional[str] = None
    owner_name: Optional[str] = None
    owner_email: Optional[str] = None


class AtlasViewCreate(AtlasViewBase):
    """Model for creating a new Atlas View"""
    pass


class AtlasViewUpdate(BaseModel):
    """Model for updating an Atlas View"""
    name: Optional[str] = None
    path: Optional[str] = None
    thumbnail_id: Optional[str] = None
    workbook_id: Optional[int] = None
    owner: Optional[str] = None
    owner_name: Optional[str] = None
    owner_email: Optional[str] = None


class AtlasViewInDB(AtlasViewBase):
    """Model for Atlas View as stored in the database"""
    id: int
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class AtlasWorkbookWithViews(AtlasWorkbookInDB):
    """Model for Atlas Workbook with its associated views"""
    views: List[AtlasViewInDB] = Field(default_factory=list)

class AtlasWorkbookOwner(BaseModel):
    owner: str
    owner_name: str
