from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime, date
from enum import Enum

class RequestType(str, Enum):
    ADHOC_DATA = "adhoc-data"
    ACCESS_PLATFORM = "access-platform"
    ACCESS_DATABASE = "access-database"
    ACCESS_REPORT = "access-report"

# class AccessLevel(str, Enum):
#     READ_ONLY = "read-only"
#     READ_WRITE = "read-write"
#     ADMIN = "admin"

class RequestStatus(str, Enum):
    PENDING = "pending"
    REVIEWED = "reviewed"
    APPROVED = "approved"
    REJECTED = "rejected"
    IN_PROGRESS = "in_progress"
    DONE = "done"

class LogType(str, Enum):
    CHANGE_STATUS = "change_status"
    CHANGE_REQUESTER = "change_requester"
    CHANGE_APPROVER = "change_approver"
    CHANGE_EXECUTOR = "change_executor"

class RequestBase(BaseModel):
    title: str
    description: Optional[str] = None
    request_type: RequestType
    data_needed: Optional[str] = None
    platform_name: Optional[str] = None
    db_name: Optional[str] = None
    report_needed: Optional[str] = None #json
    due_date: Optional[date] = None

class RequestCreate(RequestBase):
    requester_id: Optional[int] = None
    reviewer_id: Optional[int] = None
    approver_id: Optional[int] = None
    executor_id: Optional[int] = None
    executors: Optional[List[str]] = None

# class RequestUpdate(BaseModel):
#     status: Optional[RequestStatus] = None
#     report_needed: Optional[dict] = None

# class RequestUpdate(RequestCreate):
#     status: Optional[RequestStatus] = None

class RequestInDB(RequestBase):
    id: int
    status: RequestStatus
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class RequestCommentBase(BaseModel):
    comment: str

class RequestCommentCreate(RequestCommentBase):
    pass

class RequestCommentInDB(RequestCommentBase):
    id: int
    request_id: int
    user_id: Optional[int] = None
    created_at: datetime

    class Config:
        from_attributes = True

class RequestLogBase(BaseModel):
    log_type: LogType
    value: str
    note: Optional[str] = None

class RequestLogCreate(RequestLogBase):
    pass

class RequestLogInDB(RequestLogBase):
    id: int
    request_id: int
    changed_by: Optional[int] = None
    created_at: datetime

    class Config:
        from_attributes = True 
        
        
