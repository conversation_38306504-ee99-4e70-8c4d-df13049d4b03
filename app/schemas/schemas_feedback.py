"""
Pydantic schemas for feedback functionality
"""

from typing import List, Optional
from pydantic import BaseModel, Field
from datetime import datetime


class AttachmentInfo(BaseModel):
    """Schema for attachment information"""
    id: int = Field(..., description="File ID")
    # name: str = Field(..., description="Filename with extension")
    # size: int = Field(..., description="File size in bytes")


class FeedbackCreate(BaseModel):
    """Schema for creating feedback"""
    subject: str = Field(..., max_length=255, description="Feedback subject")
    category: str = Field(..., max_length=100, description="Feedback category")
    description: str = Field(..., description="Detailed feedback description")
    priority: str = Field(..., max_length=50, description="Priority level (low, medium, high, urgent)")
    attachments: Optional[List[AttachmentInfo]] = Field(default=[], description="List of attachment information")


class FeedbackResponse(BaseModel):
    """Schema for feedback response"""
    id: int
    subject: str
    category: str
    description: str
    priority: str
    attachments: Optional[List[AttachmentInfo]] = None
    user_id: Optional[int] = None
    status: str
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True


class FeedbackCreateResponse(BaseModel):
    """Schema for feedback creation response"""
    message: str
    feedback_id: int
    status: str = "success"
