"""
To avoid confusion between the SQLAlchemy models and the Pydantic models, we will have the file models.py with the SQLAlchemy models, and the file ORM.py with the Pydantic models.
"""

# from __future__ import annotations
from typing import Any, List, Optional, Union
from pydantic import BaseModel, Field


# class User(BaseModel):
#     id: int
#     username: str
#     is_admin: bool
#     status: str
#     role: List[str]
#     role_lvl1: List[str]

#     class Config:
#         orm_mode = True


class LoginRequest(BaseModel):
    username: str
    password: str


# class AuthResponse(BaseModel):
#     status: int = Field(
#         ...,
#         example=1,
#     )
#     message: Optional[str] = None
#     access_token: Optional[str] = Field(
#         ...,
#         example="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6Imh1eWhoMiIsImRpc3BsYXlOYW1lIjoiSHV5LiBIb1x1MDBlMG5nIEhcdTFlYTNpICgyKSIsInZuZ190b2tlbl9pZCI6IiIsInZuZ190b2tlbl9leHBpcmVkIjoiIiwiZXhwIjoxNjYzMDM5MDY3fQ.g4hV48P2DMzbKhtOUIPKE_BEA_0XikqNpAx38goIovw"
#     )

class Message(BaseModel):
    message: str
    
class WishRequest(BaseModel):
    name: str
    content: str
    relation: str

class UserUpdateRequest(BaseModel):
    username: str
    display_name: Optional[str] = None
    role: Optional[str] = None
    status: Optional[str] = None