"""
Pydantic models for approval configuration
"""

from typing import Optional, List
from pydantic import BaseModel
from datetime import datetime


class ApprovalConfigBase(BaseModel):
    request_type: str
    platform: str
    role: str
    user_id: int


class ApprovalConfigCreate(ApprovalConfigBase):
    pass


class ApprovalConfigUpdate(BaseModel):
    request_type: Optional[str] = None
    platform: Optional[str] = None
    role: Optional[str] = None
    user_id: Optional[int] = None


class ApprovalConfigResponse(ApprovalConfigBase):
    id: int
    created_at: datetime

    class Config:
        orm_mode = True


class ApprovalConfigBulkCreate(BaseModel):
    configs: List[ApprovalConfigCreate]
