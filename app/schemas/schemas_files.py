"""
Pydantic schemas for file management functionality
"""

from typing import Optional, List
from pydantic import BaseModel
from datetime import datetime


class FileUploadResponse(BaseModel):
    """Schema for file upload response"""
    message: str
    file_id: int
    filename: str
    original_filename: str
    file_size: int
    file_size_mb: float
    status: str = "success"


class FileInfo(BaseModel):
    """Schema for file information"""
    id: int
    filename: str
    original_filename: str
    file_path: str
    file_size: int
    file_size_mb: float
    mime_type: Optional[str] = None
    description: Optional[str] = None
    user_id: Optional[int] = None
    status: str
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True


class FileListResponse(BaseModel):
    """Schema for file list response"""
    files: List[FileInfo]
    total_count: int
    page: int
    limit: int


class FileDeleteResponse(BaseModel):
    """Schema for file deletion response"""
    message: str
    file_id: int
    status: str = "success"


class FileDownloadInfo(BaseModel):
    """Schema for file download information"""
    filename: str
    original_filename: str
    file_path: str
    mime_type: Optional[str] = None
    file_size: int
