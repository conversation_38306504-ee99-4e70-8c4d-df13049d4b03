"""
To avoid confusion between the SQLAlchemy models and the Pydantic models, we will have the file models.py with the SQLAlchemy models, and the file ORM.py with the Pydantic models.
"""

# from __future__ import annotations
from typing import Any, List, Optional, Union
from pydantic import BaseModel, Field
from datetime import datetime
class NotificationBase(BaseModel):
    user_id: int
    title: str
    content: str
    type: str
    is_read: bool = False
    
class NotificationCreate(NotificationBase):
    pass

class NotificationResponse(NotificationBase):
    id: int
    created_at: datetime
    
    class Config:
        orm_mode = True