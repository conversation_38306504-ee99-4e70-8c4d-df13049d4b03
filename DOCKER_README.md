# ZDS Platform Docker Setup

This Docker Compose setup provides a complete development and production environment for the ZDS Platform.

## 🚀 Quick Start

### Simple Mode (App Only - Recommended)

1. **Copy environment file:**
   ```bash
   cp .env.example .env
   ```

2. **Update `.env` file with your existing database configuration**

3. **Start application:**
   ```bash
   ./scripts/start-simple.sh
   ```
   Or manually:
   ```bash
   docker-compose -f docker-compose.simple.yml up --build
   ```

### Development Mode (App + Redis)

1. **Copy environment file:**
   ```bash
   cp .env.example .env
   ```

2. **Update configuration in `.env` file with your database details**

3. **Start development environment:**
   ```bash
   ./scripts/start-dev.sh
   ```
   Or manually:
   ```bash
   docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --build
   ```

4. **Access the application:**
   - API Documentation: http://localhost:3014/docs
   - Database: Your existing PostgreSQL connection

### Production Mode

1. **Ensure `.env` file is properly configured for production**

2. **Start production environment:**
   ```bash
   ./scripts/start-prod.sh
   ```
   Or manually:
   ```bash
   docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d --build
   ```

3. **Access the application:**
   - API: http://localhost:3014

## 🛠️ Services

### Core Services

- **app**: FastAPI application (Port 3014)
- **redis**: Redis cache (Port 6379) - Optional, only in full mode

### External Services

- **PostgreSQL**: Your existing database connection (configured in .env)

## 📁 Directory Structure

```
.
├── app/                    # Application source code
├── uploads/               # File uploads (mounted as volume)
├── logs/                  # Application logs
├── scripts/               # Startup scripts
├── docker-compose.simple.yml # Simple app-only setup
├── docker-compose.yml     # Full setup with Redis
├── docker-compose.dev.yml # Development overrides
├── docker-compose.prod.yml# Production overrides
├── Dockerfile             # Application container
├── requirements.txt       # Python dependencies
└── .env.example          # Environment template
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `PG_HOST` | Database host | your_db_host |
| `PG_PORT` | Database port | 5432 |
| `PG_USER` | Database user | your_db_user |
| `PG_PASSWORD` | Database password | your_db_password |
| `PG_DB` | Database name | your_db_name |
| `BASE_URL` | API base URL | /api/v1 |
| `FE_URL` | Frontend URL | http://localhost:3000 |
| `BE_URL` | Backend URL | http://localhost:3014 |
| `SECRET_KEY` | JWT secret key | (change in production) |

## 🗄️ Database

You need to run the database migration on your existing PostgreSQL database to create the required tables.

### Database Migration

Run the migration script on your existing database:

```bash
# Option 1: Use the Python migration script
python run_migration.py

# Option 2: Run SQL directly on your database
psql -h your_host -U your_user -d your_database -f database_migration.sql
```

## 📊 Monitoring

### View Logs

```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f app
docker-compose logs -f postgres
```

### Health Checks

All services include health checks. Check status:

```bash
docker-compose ps
```

## 🛑 Stopping Services

```bash
./scripts/stop.sh
```

Or manually:

```bash
docker-compose down
```

## 🔄 Updates

### Rebuild Application

```bash
docker-compose build app
docker-compose up -d app
```

### Update Dependencies

1. Update `requirements.txt`
2. Rebuild container:
   ```bash
   docker-compose build --no-cache app
   ```

## 🐛 Troubleshooting

### Common Issues

1. **Port conflicts**: Change ports in docker-compose.yml
2. **Permission issues**: Ensure uploads directory is writable
3. **Database connection**: Check database is healthy before app starts

### Reset Everything

```bash
docker-compose down -v
docker system prune -f
./scripts/start-dev.sh
```

## 🔒 Security Notes

- Change default passwords in production
- Use strong SECRET_KEY
- Don't expose database ports in production
- Use HTTPS in production with reverse proxy

## 📝 Development

### Hot Reload

Development mode includes hot reload - changes to Python files will automatically restart the application.

### Database Migrations

Add new migration scripts to the `database_migration.sql` file or create separate migration files.

## 🚀 Production Deployment

For production deployment:

1. Use `docker-compose.prod.yml`
2. Set proper environment variables
3. Use external database if needed
4. Set up reverse proxy (nginx/traefik)
5. Configure SSL certificates
6. Set up monitoring and logging
