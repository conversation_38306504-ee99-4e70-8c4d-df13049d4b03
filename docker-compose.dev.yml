version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3009:3009"
    network_mode: host      
    volumes:
      # Mount source code for hot reload in development
      - ./app:/app
      - ./uploads:/uploads
      - ./logs:/logs
    # environment:
    #   # Enable debug mode
    #   DEBUG: "true"
    #   # Enable auto-reload
    #   RELOAD: "true"
    # command: ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "3014", "--reload", "--log-level", "debug"]
    env_file:
      - .env
    # environment:
    #   - PYTHONUNBUFFERED=1
    # command: hypercorn main:app --bind 0.0.0.0:3009 --log-level DEBUG --workers 3 --reload 
    command: uvicorn main:app --reload --host 0.0.0.0 --port 3009