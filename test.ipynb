{"cells": [{"cell_type": "code", "execution_count": 1, "id": "3c89b179", "metadata": {}, "outputs": [], "source": ["VNG_AUTHENTICATION_URL = \"https://security.vng.com.vn/token-gateway/api/verify_otp\""]}, {"cell_type": "code", "execution_count": 2, "id": "e8355166", "metadata": {}, "outputs": [], "source": ["import requests"]}, {"cell_type": "code", "execution_count": null, "id": "af3c517f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Valid OTP\n"]}], "source": ["username = 'huyhh2'\n", "password = '121122157044'\n", "data = {\n", "    \"username\": username,\n", "    \"code\": password,\n", "    \"type\": \"ga\"\n", "}\n", "response = requests.post(VNG_AUTHENTICATION_URL, data=data)\n", "if response.status_code == 200:\n", "    result_body = response.json()\n", "    is_valid = result_body['status']\n", "    if is_valid == \"true\":\n", "        print(\"Valid OTP\")\n", "        print(result_body)\n", "    else:\n", "        print(\"Invalid OTP\")\n", "else:\n", "    print(\"Error:\", response.status_code)"]}, {"cell_type": "code", "execution_count": 4, "id": "6fd789a7", "metadata": {}, "outputs": [], "source": ["result_body = response.json()"]}, {"cell_type": "code", "execution_count": 5, "id": "fef370c8", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'status': 'true'}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["result_body"]}, {"cell_type": "code", "execution_count": 6, "id": "a0b0e76f", "metadata": {}, "outputs": [], "source": ["response = requests.post(VNG_AUTHENTICATION_URL, data=data)"]}, {"cell_type": "code", "execution_count": 7, "id": "112c50a4", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Response [200]>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["response"]}], "metadata": {"kernelspec": {"display_name": "zdscoding", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}}, "nbformat": 4, "nbformat_minor": 5}